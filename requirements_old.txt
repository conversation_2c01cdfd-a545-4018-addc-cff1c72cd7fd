absl-py==1.4.0
aiohttp @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_croot-ap8dgzop/aiohttp_1646819228834/work
aiosignal @ file:///tmp/build/80754af9/aiosignal_1637843061372/work
antlr4-python3-runtime==4.9.3
astunparse==1.6.3
async-timeout @ file:///tmp/build/80754af9/async-timeout_1637851218186/work
attrs @ file:///opt/conda/conda-bld/attrs_1642510447205/work
black==25.1.0
blinker==1.4
brotlipy==0.7.0
cachetools @ file:///tmp/build/80754af9/cachetools_1619597386817/work
certifi @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_884c889c-96af-444f-bd6d-daddb5e9a462ykj3l5n_/croots/recipe/certifi_1655968814730/work/certifi
cffi @ file:///Users/<USER>/ci_310/cffi_1643911190082/work
charset-normalizer @ file:///tmp/build/80754af9/charset-normalizer_1630003229654/work
click @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_croot-quwchbn8/click_1646123324461/work
cloudpickle==3.1.1
ConfigArgParse @ file:///tmp/build/80754af9/configargparse_1615837118917/work
contourpy==1.0.5
cryptography @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_5871d1ea-0250-4cd7-ac89-4b1e60514f5daqk8t0ow/croots/recipe/cryptography_1652101128666/work
cycler==0.11.0
dash==2.7.0
dash-core-components==2.0.0
dash-html-components==2.0.0
dash-table==5.0.0
-e git+https://github.com/facebookresearch/detectron2@536dc9d527074e3b15df5f6677ffe1f4e104a4ab#egg=detectron2
fastjsonschema==2.16.2
fire==0.4.0
Flask==2.2.2
flatbuffers==23.5.9
fonttools==4.37.4
freetype-py==2.3.0
frozenlist @ file:///Users/<USER>/ci_310/frozenlist_1643965854417/work
fsspec==2022.10.0
future @ file:///Users/<USER>/ci_310/future_1643965899852/work
fvcore==0.1.5.post20221221
gast==0.4.0
glcontext==2.3.7
google-auth==2.18.1
google-auth-oauthlib==1.0.0
google-pasta==0.2.0
grpcio==1.55.0
h5py==3.8.0
hydra-core==1.3.2
idna @ file:///tmp/build/80754af9/idna_1637925883363/work
imageio==2.28.1
importlib-metadata @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_croot-5pqd2z6f/importlib-metadata_1648710902288/work
install==1.3.5
iopath==0.1.9
itsdangerous==2.1.2
Jinja2==3.1.2
joblib @ file:///tmp/build/80754af9/joblib_1635411271373/work
jsonschema==4.17.0
jupyter_core==5.0.0
keras==2.13.1rc0
kiwisolver==1.4.4
layoutparser==0.3.4
lazy_loader==0.2
libclang==16.0.0
lightning-lite==1.8.0.post1
lightning-utilities==0.3.0
loguru==0.7.0
Markdown @ file:///Users/<USER>/ci_310/markdown_1643973487353/work
MarkupSafe==2.1.1
matplotlib==3.6.1
moderngl==5.7.2
multidict @ file:///Users/<USER>/ci_310/multidict_1643966777153/work
multipledispatch==0.6.0
mypy_extensions==1.1.0
natsort==8.4.0
nbformat==5.5.0
networkx==3.1
numpy==1.26.0
oauthlib==3.1.0
omegaconf==2.3.0
open3d==0.16.0
opencv-python==********
opt-einsum==3.3.0
packaging==25.0
pandas==2.0.3
pathspec==0.12.1
pdf2image==1.17.0
pdfminer.six==20250327
pdfplumber==0.11.6
pillow==11.2.1
platformdirs==2.5.4
plotly==5.11.0
plyfile==0.7.4
polyscope==1.3.1
portalocker==3.1.1
protobuf==4.23.1
pyasn1 @ file:///Users/<USER>/demo/mc3/conda-bld/pyasn1_1629708007385/work
pyasn1-modules==0.2.8
pycocotools==2.0.8
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pyglet==2.0.0
PyJWT @ file:///home/<USER>/feedstock_root/build_artifacts/pyjwt_1663432570896/work
pykdtree @ file:///Users/<USER>/ci_310/pykdtree_1644262738212/work
PyOpenGL==3.1.0
pyOpenSSL @ file:///opt/conda/conda-bld/pyopenssl_1643788558760/work
pyparsing==3.0.9
pypdfium2==4.30.1
pyrender==0.1.45
pyrr==0.10.3
pyrsistent==0.19.2
PySocks @ file:///Users/<USER>/ci_310/pysocks_1643961536721/work
python-dateutil==2.8.2
pytorch-lightning==1.8.0.post1
pytz==2023.3.post1
PyWavelets==1.4.1
PyYAML==6.0
requests @ file:///opt/conda/conda-bld/requests_1641824580448/work
requests-oauthlib==1.3.0
rsa @ file:///tmp/build/80754af9/rsa_1614366226499/work
Rtree @ file:///Users/<USER>/ci_310/rtree_1643968618205/work
scikit-image==0.20.0
scikit-learn @ file:///Users/<USER>/ci_310/scikit-learn_1644264513665/work
scipy==1.10.1
shapely==2.1.1
simple-3dviz==0.7.0
six @ file:///tmp/build/80754af9/six_1644875935023/work
smplx==0.1.28
tabulate==0.9.0
tenacity==8.1.0
tensorboard==2.13.0
tensorboard-data-server==0.7.0
tensorboard-plugin-wit==1.6.0
tensorflow==2.13.0rc0
tensorflow-estimator==2.13.0rc0
tensorflow-macos==2.13.0rc0
termcolor==2.1.0
threadpoolctl @ file:///Users/<USER>/demo/mc3/conda-bld/threadpoolctl_1629802263681/work
tifffile==2023.4.12
tomli==2.2.1
torch==1.12.1
torchmetrics==0.10.2
torchvision==0.13.1
tqdm==4.64.1
traitlets==5.5.0
trimesh @ file:///home/<USER>/feedstock_root/build_artifacts/trimesh_1664841281434/work
typing_extensions @ file:///opt/conda/conda-bld/typing_extensions_1647553014482/work
tzdata==2023.3
urllib3 @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_e1a192c4-15be-4e72-95cc-219f6405cdb5nmvan_lb/croots/recipe/urllib3_1650637219436/work
Werkzeug==2.2.2
wget==3.2
wrapt==1.14.1
wxPython==4.2.0
yacs==0.1.8
yarl @ file:///Users/<USER>/ci_310/yarl_1643972783774/work
zipp @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_66c2c5f2-5dd5-4946-a16a-72af650ebd6cnmz4ou0f/croots/recipe/zipp_1652343960956/work
