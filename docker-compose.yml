version: '3.8'

services:
  eigenfit-api:
    build: 
      context: .
      dockerfile: dockerfile
    container_name: eigenfit-foot-measurement
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - eigenfit-network

networks:
  eigenfit-network:
    driver: bridge

volumes:
  eigenfit-data:
  eigenfit-logs:
