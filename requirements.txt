# Core dependencies for EigenFit Foot Measurement System
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Computer Vision and Image Processing
opencv-python==********
numpy==1.24.3
pillow==10.0.1
scikit-image==0.21.0

# Layout Analysis
layoutparser==0.3.4

# Geometry and Math
shapely==2.0.2
scipy==1.11.3

# Data Processing
pandas==2.0.3

# Utilities
pyyaml==6.0.1
tqdm==4.66.1
pathlib2==2.3.7

# Optional: For enhanced foot detection
mediapipe==0.10.7

# Development and Testing (optional)
pytest==7.4.2
black==23.9.1
