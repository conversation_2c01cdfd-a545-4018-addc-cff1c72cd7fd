name: NDF
channels:
  - pytorch
  - anaconda
  - conda-forge
channel_priority: strict
dependencies:
  - aiosignal=1.2.0
  - async-timeout=4.0.1
  - attrs=21.4.0
  - blas=2.113
  - blas-devel=3.9.0
  - bzip2=1.0.8
  - c-ares=1.18.1
  - ca-certificates=2022.4.26
  - cachetools=4.2.2
  - charset-normalizer=2.0.4
  - configargparse=1.4
  - dataclasses=0.8
  - ffmpeg=4.2.2
  - freetype=2.11.0
  - gettext=0.21.0
  - giflib=5.2.1
  - gmp=6.2.1
  - gnutls=3.6.15
  - icu=68.1
  - idna=3.3
  - joblib=1.1.0
  - jpeg=9e
  - lame=3.100
  - lcms2=2.12
  - libblas=3.9.0
  - libcblas=3.9.0
  - libffi=3.4.2
  - libgfortran5=11.2.0
  - libiconv=1.16
  - libidn2=2.3.1
  - liblapack=3.9.0
  - liblapacke=3.9.0
  - libopenblas=0.3.18
  - libopus=1.3
  - libpng=1.6.37
  - libprotobuf=3.20.1
  - libspatialindex=1.9.3
  - libtasn1=4.16.0
  - libtiff=4.2.0
  - libunistring=0.9.10
  - libwebp=1.2.2
  - libwebp-base=1.2.2
  - libxml2=2.9.14
  - lz4-c=1.9.3
  - multidict
  - ncurses=6.3
  - nettle=3.7.3
  - numpy-base=1.22.3
  - oauthlib=3.1.0
  - openblas=0.3.18
  - openh264=1.8.0
  - openssl=1.1.1o
  - pyasn1=0.4.8
  - pyasn1-modules=0.2.8
  - pycparser=2.21
  - pyjwt=2.5.0
  - pykdtree==1.3.4
  - pyopenssl=22.0.0
  - python=3.10.4
  - python_abi=3.10
  - pytorch=1.12.1
  - readline=8.1.2
  - requests=2.27.1
  - requests-oauthlib=1.3.0
  - rsa=4.7.2
  - six=1.16.0
  - sqlite=3.38.5
  - tensorboard-plugin-wit=1.6.0
  - threadpoolctl=2.2.0
  - tk=8.6.12
  - trimesh=3.15.3
  - typing-extensions=4.1.1
  - typing_extensions=4.1.1
  - wheel=0.37.1
  - wrapt=1.14.1
  - x264=1!152.20180806
  - xz=5.2.5
  - yaml=0.2.5
  - yarl
  - zlib=1.2.12
  - zstd=1.5.2
  
  - pip:
    - absl-py==1.4.0
    - aiohttp==3.8.1
    - antlr4-python3-runtime==4.9.3
    - astunparse==1.6.3
    - black==25.1.0
    - blinker==1.4
    - brotlipy==0.7.0
    - certifi==2022.6.15
    - cffi==1.15.0
    - click==8.0.4
    - cloudpickle==3.1.1
    - contourpy==1.0.5
    - cryptography==37.0.1
    - cycler==0.11.0
    - dash==2.7.0
    - dash-core-components==2.0.0
    - dash-html-components==2.0.0
    - dash-table==5.0.0
    - fastjsonschema==2.16.2
    - fire==0.4.0
    - flask==2.2.2
    - flatbuffers==23.5.9
    - fonttools==4.37.4
    - freetype-py==2.3.0
    - frozenlist==1.2.0
    - fsspec==2022.10.0
    - future==0.18.2
    - fvcore==0.1.5.post20221221
    - gast==0.4.0
    - glcontext==2.3.7
    - google-auth==2.18.1
    - google-auth-oauthlib==1.0.0
    - google-pasta==0.2.0
    - grpcio==1.55.0
    - h5py==3.8.0
    - hydra-core==1.3.2
    - imageio==2.28.1
    - importlib-metadata==4.11.3
    - iopath==0.1.9
    - itsdangerous==2.1.2
    - jinja2==3.1.2
    - jsonschema==4.17.0
    - jupyter-core==5.0.0
    - keras==2.13.1rc0
    - kiwisolver==1.4.4
    - layoutparser==0.3.4
    - lazy-loader==0.2
    - libclang==16.0.0
    - lightning-utilities==0.3.0
    - loguru==0.7.0
    - markdown==3.3.4
    - markupsafe==2.1.1
    - matplotlib==3.6.1
    - moderngl==5.7.2
    - multipledispatch==0.6.0
    - mypy-extensions==1.1.0
    - natsort==8.4.0
    - nbformat==5.5.0
    - networkx==3.1
    - numpy==1.26.0
    - omegaconf==2.3.0
    - open3d==0.16.0
    - opencv-python==********
    - opt-einsum==3.3.0
    - packaging==25.0
    - pandas==2.0.3
    - pathspec==0.12.1
    - pdf2image==1.17.0
    - pdfminer-six==20250327
    - pdfplumber==0.11.6
    - pillow==11.2.1
    - pip==21.2.4
    - platformdirs==2.5.4
    - plotly==5.11.0
    - plyfile==0.7.4
    - portalocker==3.1.1
    - protobuf==4.23.1
    - pycocotools==2.0.8
    - pyglet==2.0.0
    - pyopengl==3.1.0
    - pyparsing==3.0.9
    - pypdfium2==4.30.1
    - pyrender==0.1.45
    - pyrr==0.10.3
    - pyrsistent==0.19.2
    - pysocks==1.7.1
    - python-dateutil==2.8.2
    - pytz==2023.3.post1
    - pywavelets==1.4.1
    - pyyaml==6.0
    - rtree==0.9.7
    - scikit-image==0.20.0
    - scikit-learn==1.0.2
    - scipy==1.10.1
    - setuptools==61.2.0
    - shapely==2.1.1
    - simple-3dviz==0.7.0
    - smplx==0.1.28
    - tabulate==0.9.0
    - tenacity==8.1.0
    - tensorboard==2.13.0
    - tensorboard-data-server==0.7.0
    - termcolor==2.1.0
    - tifffile==2023.4.12
    - tomli==2.2.1
    - torch==1.12.1
    - torchmetrics==0.10.2
    - torchvision==0.13.1
    - tqdm==4.64.1
    - traitlets==5.5.0
    - tzdata==2023.3
    - urllib3==1.26.9
    - werkzeug==2.2.2
    - wget==3.2
    - wrapt==1.14.1
    - yacs==0.1.8
    - zipp==3.8.0
prefix: /opt/homebrew/Caskroom/miniforge/base/envs/NDF
