# EigenFit Deployment Guide

## Quick Start

### 1. Using Docker (Recommended)

```bash
# Build and run with <PERSON><PERSON> Compose
docker-compose up --build -d

# Or build and run manually
docker build -t eigenfit-foot-measurement .
docker run -p 8000:8000 eigenfit-foot-measurement
```

### 2. Local Development

```bash
# Install dependencies
pip install -r requirements_clean.txt

# Start development server
python start_server.py

# Or use uvicorn directly
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 3. Test the API

```bash
# Run automated tests
python test_api.py

# Or test manually
curl http://localhost:8000/health
```

## API Usage Examples

### Health Check
```bash
curl -X GET "http://localhost:8000/health"
```

### Foot Measurement
```bash
curl -X POST "http://localhost:8000/measure" \
  -H "Content-Type: multipart/form-data" \
  -F "images=@left_top.jpg" \
  -F "images=@right_top.jpg" \
  -F "images=@left_side.jpg" \
  -F "images=@right_side.jpg"
```

### Python Client Example
```python
import requests

# Upload images for measurement
files = [
    ('images', open('left_top.jpg', 'rb')),
    ('images', open('right_top.jpg', 'rb')),
    ('images', open('left_side.jpg', 'rb')),
    ('images', open('right_side.jpg', 'rb'))
]

response = requests.post('http://localhost:8000/measure', files=files)
measurements = response.json()

print(f"Left foot length: {measurements['leftFoot']['heelToToeLength']} mm")
print(f"Right foot length: {measurements['rightFoot']['heelToToeLength']} mm")
```

## Production Deployment

### Environment Variables
```bash
export LOG_LEVEL=INFO
export PYTHONPATH=/app
export MODEL_PATH=/app/models
```

### Docker Production
```bash
# Build production image
docker build -t eigenfit-foot-measurement:latest .

# Run with production settings
docker run -d \
  --name eigenfit-api \
  -p 8000:8000 \
  -e LOG_LEVEL=WARNING \
  --restart unless-stopped \
  eigenfit-foot-measurement:latest
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: eigenfit-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: eigenfit-api
  template:
    metadata:
      labels:
        app: eigenfit-api
    spec:
      containers:
      - name: eigenfit-api
        image: eigenfit-foot-measurement:latest
        ports:
        - containerPort: 8000
        env:
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: eigenfit-service
spec:
  selector:
    app: eigenfit-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

## Monitoring and Logging

### Health Checks
- Endpoint: `GET /health`
- Expected Response: `{"status": "healthy", "service": "eigenfit-foot-measurement"}`

### Logging
- Application logs are written to stdout
- Set `LOG_LEVEL` environment variable (DEBUG, INFO, WARNING, ERROR)

### Metrics
- Response times
- Error rates
- Memory usage
- CPU usage

## Troubleshooting

### Common Issues

1. **Model files missing**
   ```bash
   # Ensure these files exist:
   ls -la models/config.yml
   ls -la models/model.pth
   ```

2. **Memory issues**
   ```bash
   # Increase Docker memory limit
   docker run --memory=4g eigenfit-foot-measurement
   ```

3. **Permission errors**
   ```bash
   # Fix file permissions
   chmod +x start_server.py test_api.py
   ```

### Debug Mode
```python
# Enable debug logging in the API
session = EigenFitSession(img_paths, debug=True)
```

## Security Considerations

1. **Input Validation**: API validates file types and sizes
2. **Temporary Files**: Automatically cleaned up after processing
3. **Resource Limits**: Configure appropriate memory and CPU limits
4. **Network Security**: Use HTTPS in production
5. **Authentication**: Add authentication layer for production use

## Performance Optimization

1. **GPU Support**: Enable CUDA for faster processing
2. **Caching**: Cache model loading for better performance
3. **Load Balancing**: Use multiple replicas for high traffic
4. **Image Preprocessing**: Optimize image sizes before processing

## Backup and Recovery

1. **Model Files**: Backup `models/` directory
2. **Configuration**: Version control all config files
3. **Data**: No persistent data stored by default
4. **Logs**: Configure log rotation and archival
