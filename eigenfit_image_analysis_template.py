"""
EigenFit Foot Measurement – Python Template
==========================================

A **lean, implementation-ready skeleton** for extracting physical foot
measurements from **a specific set of four smartphone images**:

1. Left foot from top view
2. Right foot from top view
3. Left foot inner side view (for arch calculation)
4. Right foot inner side view (for arch calculation)

Key responsibilities
--------------------
1. **I/O & validation** – load the four images and verify they meet the agreed spec.
2. **Calibration** – detect the A4 sheet in *each* image to derive a per‑image
   millimetre‑per‑pixel scale.
3. **Foot detection & keypoints** – isolate the foot in every view and detect required
   keypoints based on the view type (top or inner side).
4. **Geometry** – compute physical measurements from keypoints appropriate to each view.
5. **Aggregation** – combine multi‑view data into a single JSON payload with complete
   measurements for both feet.
6. **Orientation Handling** - detect and handle both portrait and landscape orientations.

Project layout
-------------
```
├── eigenfit_image_analysis_template.py  # ← this file (imported as module)
├── app.py                               # FastAPI web service
├── dockerfile                           # Container definition
└── requirements.txt                     # Dependencies
```
"""

from __future__ import annotations

import json
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Sequence, Tuple

# Third‑party libraries – declare in requirements.txt
import cv2  # type: ignore
import numpy as np  # type: ignore

# Optional: Mediapipe can be handy for keypoint detection
try:
    import mediapipe as mp  # type: ignore
except ImportError:  # pragma: no cover
    mp = None

###############################################################################
# Configuration & logging                                                     #
###############################################################################

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)-8s | %(name)s | %(message)s",
)
logger = logging.getLogger("eigenfit")

# Physical constants
A4_WIDTH_MM: float = 210.0
A4_HEIGHT_MM: float = 297.0

# ---------------------------------------------------------------------------
# Standardised input‑image specification (guaranteed upstream)
# ---------------------------------------------------------------------------
STANDARD_LANDSCAPE_WIDTH_PX: int = 2048  # long side (landscape)
STANDARD_LANDSCAPE_HEIGHT_PX: int = 1536  # short side (landscape)
STANDARD_PORTRAIT_WIDTH_PX: int = 1536  # short side (portrait)
STANDARD_PORTRAIT_HEIGHT_PX: int = 2048  # long side (portrait)

# Expected number of images per session
NUM_EXPECTED_IMAGES: int = 4

# Image orientation
ORIENTATION_LANDSCAPE = "landscape"
ORIENTATION_PORTRAIT = "portrait"

###############################################################################
# Data classes                                                                 #
###############################################################################


@dataclass
class TopViewFootKeypoints:
    """Pixel coordinates of foot landmarks from top view (all in *px*)."""

    heel: Tuple[int, int]
    toe: Tuple[int, int]
    inner_width: Tuple[int, int]
    outer_width: Tuple[int, int]


@dataclass
class SideViewFootKeypoints:
    """Pixel coordinates of foot landmarks from inner side view (all in *px*)."""

    arch_back: Tuple[int, int]
    arch_front: Tuple[int, int]
    arch_low: Tuple[int, int]
    arch_high: Tuple[int, int]


@dataclass
class FootMeasurements:
    """Physical metrics in millimetres."""

    heel_to_toe_length: float
    foot_max_width: float
    arch_length: float
    arch_height: float

    def to_json_dict(self) -> Dict[str, float]:
        return {
            "heelToToeLength": self.heel_to_toe_length,
            "footMaxWidth": self.foot_max_width,
            "archLength": self.arch_length,
            "archHeight": self.arch_height,
        }


###############################################################################
# Core functional classes                                                     #
###############################################################################


class Calibrator:
    """Locate the A4 sheet and estimate the *mm per pixel* scale factor."""

    def __init__(self, img: np.ndarray, *, debug: bool = False) -> None:
        self.img = img
        self.debug = debug

    def find_paper_contour(self) -> np.ndarray:
        """Return the largest 4‑point contour that resembles A4 paper."""
        # Convert to HSV for better white paper detection
        hsv = cv2.cvtColor(self.img, cv2.COLOR_BGR2HSV)

        # Create mask for white/light colored areas (A4 paper)
        mask = cv2.inRange(hsv, (0, 0, 125), (180, 30, 255))

        # Morphological operations to clean up the mask
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
        mask_closed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_close)

        # Edge detection
        edges = cv2.Canny(mask_closed, 100, 150)

        # Close gaps in edges
        kernel_close2 = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close2)

        # Find contours
        contours, _ = cv2.findContours(edges_closed, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise RuntimeError("No contours found for A4 paper detection")

        # Find the largest contour (likely the A4 paper)
        largest_contour = max(contours, key=cv2.contourArea)

        # Approximate to quadrilateral
        peri = cv2.arcLength(largest_contour, True)
        approx = cv2.approxPolyDP(largest_contour, 0.02 * peri, True)

        # If we don't get exactly 4 points, use convex hull and try again
        if len(approx) != 4:
            hull = cv2.convexHull(largest_contour)
            peri = cv2.arcLength(hull, True)
            approx = cv2.approxPolyDP(hull, 0.02 * peri, True)

            # If still not 4 points, use bounding rectangle
            if len(approx) != 4:
                x, y, w, h = cv2.boundingRect(largest_contour)
                approx = np.array([[x, y], [x+w, y], [x+w, y+h], [x, y+h]], dtype=np.int32)
                approx = approx.reshape(-1, 1, 2)

        return approx

    def mm_per_px(self) -> float:
        """Compute millimetres‑per‑pixel scale using the detected contour."""
        try:
            paper_contour = self.find_paper_contour()

            # Order points: top-left, top-right, bottom-right, bottom-left
            pts = paper_contour.reshape(-1, 2)
            rect = self._order_points(pts)

            # Calculate width and height in pixels
            width_px = np.linalg.norm(rect[1] - rect[0])
            height_px = np.linalg.norm(rect[2] - rect[1])

            # A4 dimensions in mm
            a4_width_mm = A4_WIDTH_MM
            a4_height_mm = A4_HEIGHT_MM

            # Calculate scale factors for both dimensions
            scale_x = a4_width_mm / width_px
            scale_y = a4_height_mm / height_px

            # Return average scale (assuming minimal perspective distortion)
            return (scale_x + scale_y) / 2.0

        except Exception as e:
            if self.debug:
                logger.warning(f"Failed to detect A4 paper, using default scale: {e}")
            # Fallback: assume standard smartphone photo resolution
            # Typical A4 paper in smartphone photo is around 800-1200 pixels wide
            return A4_WIDTH_MM / 1000.0  # Default scale factor

    def _order_points(self, pts: np.ndarray) -> np.ndarray:
        """Order 4 points in the order: top-left, top-right, bottom-right, bottom-left."""
        rect = np.zeros((4, 2), dtype="float32")
        s = pts.sum(axis=1)
        diff = np.diff(pts, axis=1)
        rect[0] = pts[np.argmin(s)]      # top-left
        rect[2] = pts[np.argmax(s)]      # bottom-right
        rect[1] = pts[np.argmin(diff)]   # top-right
        rect[3] = pts[np.argmax(diff)]   # bottom-left
        return rect


class TopViewFootDetector:
    """Extract foot region and keypoints from top view images."""

    def __init__(self, img: np.ndarray, *, debug: bool = False) -> None:
        self.img = img
        self.debug = debug

    def keypoints(self) -> TopViewFootKeypoints:
        """Return ordered keypoints for the foot in this top view image."""
        # Find foot contour
        foot_contour = self._segment_foot()

        # Extract keypoints from the contour
        heel, toe = self._find_heel_toe_points(foot_contour)
        inner_width, outer_width = self._find_width_points(foot_contour)

        return TopViewFootKeypoints(
            heel=heel,
            toe=toe,
            inner_width=inner_width,
            outer_width=outer_width
        )

    def _segment_foot(self) -> np.ndarray:
        """Segment the foot from the background."""
        # Convert to grayscale
        gray = cv2.cvtColor(self.img, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Use Otsu's thresholding to separate foot from background
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Morphological operations to clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # Find contours
        contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            raise RuntimeError("No foot contour found in top view")

        # Return the largest contour (should be the foot)
        return max(contours, key=cv2.contourArea)

    def _find_heel_toe_points(self, contour: np.ndarray) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """Find heel and toe points from the foot contour."""
        # Get the extreme points
        leftmost = tuple(contour[contour[:, :, 0].argmin()][0])
        rightmost = tuple(contour[contour[:, :, 0].argmax()][0])
        topmost = tuple(contour[contour[:, :, 1].argmin()][0])
        bottommost = tuple(contour[contour[:, :, 1].argmax()][0])

        # For a typical top view, the longest dimension is heel to toe
        # Determine orientation based on aspect ratio
        width = rightmost[0] - leftmost[0]
        height = bottommost[1] - topmost[1]

        if width > height:
            # Foot is oriented horizontally
            heel = leftmost
            toe = rightmost
        else:
            # Foot is oriented vertically
            heel = bottommost
            toe = topmost

        return heel, toe

    def _find_width_points(self, contour: np.ndarray) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """Find the widest points of the foot (inner and outer width)."""
        # Get the extreme points
        leftmost = tuple(contour[contour[:, :, 0].argmin()][0])
        rightmost = tuple(contour[contour[:, :, 0].argmax()][0])
        topmost = tuple(contour[contour[:, :, 1].argmin()][0])
        bottommost = tuple(contour[contour[:, :, 1].argmax()][0])

        # Determine orientation
        width = rightmost[0] - leftmost[0]
        height = bottommost[1] - topmost[1]

        if width > height:
            # Foot is oriented horizontally, width is vertical
            inner_width = topmost
            outer_width = bottommost
        else:
            # Foot is oriented vertically, width is horizontal
            inner_width = leftmost
            outer_width = rightmost

        return inner_width, outer_width


class SideViewFootDetector:
    """Extract foot arch keypoints from inner side view images."""

    def __init__(self, img: np.ndarray, *, debug: bool = False) -> None:
        self.img = img
        self.debug = debug

    def keypoints(self) -> SideViewFootKeypoints:
        """Return ordered keypoints for the foot arch in this side view image."""
        # Find foot contour
        foot_contour = self._segment_foot()

        # Extract arch keypoints
        arch_back, arch_front = self._find_arch_length_points(foot_contour)
        arch_low, arch_high = self._find_arch_height_points(foot_contour)

        return SideViewFootKeypoints(
            arch_back=arch_back,
            arch_front=arch_front,
            arch_low=arch_low,
            arch_high=arch_high
        )

    def _segment_foot(self) -> np.ndarray:
        """Segment the foot from the background."""
        # Convert to grayscale
        gray = cv2.cvtColor(self.img, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Use Otsu's thresholding to separate foot from background
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Morphological operations to clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # Find contours
        contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            raise RuntimeError("No foot contour found in side view")

        # Return the largest contour (should be the foot)
        return max(contours, key=cv2.contourArea)

    def _find_arch_length_points(self, contour: np.ndarray) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """Find the back and front points of the arch."""
        # Get extreme points
        leftmost = tuple(contour[contour[:, :, 0].argmin()][0])
        rightmost = tuple(contour[contour[:, :, 0].argmax()][0])

        # For side view, arch length is typically the horizontal span
        arch_back = leftmost
        arch_front = rightmost

        return arch_back, arch_front

    def _find_arch_height_points(self, contour: np.ndarray) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """Find the lowest and highest points of the arch."""
        # Get extreme points
        topmost = tuple(contour[contour[:, :, 1].argmin()][0])
        bottommost = tuple(contour[contour[:, :, 1].argmax()][0])

        # For side view, arch height is the vertical span
        # The arch low point is typically the bottom of the foot
        # The arch high point is the top of the foot in the arch area
        arch_low = bottommost

        # Find the arch high point by looking for the highest point in the middle section
        contour_points = contour.reshape(-1, 2)

        # Get the middle third of the foot (where the arch typically is)
        min_x = contour_points[:, 0].min()
        max_x = contour_points[:, 0].max()
        middle_start = min_x + (max_x - min_x) * 0.3
        middle_end = min_x + (max_x - min_x) * 0.7

        # Find points in the middle section
        middle_points = contour_points[
            (contour_points[:, 0] >= middle_start) &
            (contour_points[:, 0] <= middle_end)
        ]

        if len(middle_points) > 0:
            # Find the highest point in the middle section
            highest_idx = middle_points[:, 1].argmin()
            arch_high = tuple(middle_points[highest_idx])
        else:
            # Fallback to topmost point
            arch_high = topmost

        return arch_low, arch_high


class FootGeometry:
    """Convert *pixel* keypoints into *millimetre* measurements."""

    def __init__(self, mm_per_px: float):
        self.scale = mm_per_px

    @staticmethod
    def _distance(p1: Tuple[int, int], p2: Tuple[int, int]) -> float:
        return float(
            np.linalg.norm(np.array(p1, dtype=float) - np.array(p2, dtype=float))
        )

    def metrics_from_top_view(self, kp: TopViewFootKeypoints) -> Dict[str, float]:
        """Calculate length and width measurements from top view."""
        length_px = self._distance(kp.heel, kp.toe)
        width_px = self._distance(kp.inner_width, kp.outer_width)

        return {
            "heelToToeLength": length_px * self.scale,
            "footMaxWidth": width_px * self.scale,
        }

    def metrics_from_side_view(self, kp: SideViewFootKeypoints) -> Dict[str, float]:
        """Calculate arch measurements from side view."""
        arch_len_px = self._distance(kp.arch_back, kp.arch_front)
        arch_ht_px = self._distance(kp.arch_low, kp.arch_high)

        return {
            "archLength": arch_len_px * self.scale,
            "archHeight": arch_ht_px * self.scale,
        }


###############################################################################
# Multi‑image pipeline façade                                                 #
###############################################################################


class EigenFitSession:
    """High‑level API – process **four specific foot images** → unified JSON payload.

    The four images should be:
    1. Left foot from top view
    2. Right foot from top view
    3. Left foot inner side view (for arch calculation)
    4. Right foot inner side view (for arch calculation)
    """

    # Image indices for clarity
    LEFT_TOP_VIEW = 0
    RIGHT_TOP_VIEW = 1
    LEFT_SIDE_VIEW = 2
    RIGHT_SIDE_VIEW = 3

    def __init__(self, img_paths: Sequence[str | Path], *, debug: bool = False) -> None:
        if len(img_paths) != NUM_EXPECTED_IMAGES:
            raise ValueError(
                f"Expected {NUM_EXPECTED_IMAGES} images, got {len(img_paths)}"
            )
        self.paths = [Path(p) for p in img_paths]
        self.debug = debug
        self.imgs: List[np.ndarray] = [self._load_and_validate(p) for p in self.paths]

    # ------------------------------------------------------------------
    # Public methods
    # ------------------------------------------------------------------

    def run(self) -> Dict[str, Any]:
        """Process all four images and merge results into one JSON‑ready dict.

        The processing follows this pattern:
        1. Get length and width from top views (images 0 and 1)
        2. Get arch measurements from side views (images 2 and 3)
        3. Combine the measurements for each foot
        """
        # Initialize results structure
        result: Dict[str, Dict[str, Any]] = {"leftFoot": {}, "rightFoot": {}}

        # Process each image with its appropriate detector and measurements
        for img_idx, img in enumerate(self.imgs):
            # Normalize orientation before processing
            # Top view images are typically clearer in landscape, side view in portrait
            target_orientation = (
                ORIENTATION_LANDSCAPE
                if img_idx in (self.LEFT_TOP_VIEW, self.RIGHT_TOP_VIEW)
                else ORIENTATION_PORTRAIT
            )
            normalized_img = self.normalize_orientation(img, target_orientation)

            # For all images: calibrate to get mm per pixel scale
            calib = Calibrator(normalized_img, debug=self.debug)
            mm_per_px = calib.mm_per_px()
            geom = FootGeometry(mm_per_px)

            # Process based on image type
            if img_idx == self.LEFT_TOP_VIEW:
                # Left foot top view - get length and width
                detector = TopViewFootDetector(normalized_img, debug=self.debug)
                top_keypoints = detector.keypoints()
                result["leftFoot"].update(geom.metrics_from_top_view(top_keypoints))

            elif img_idx == self.RIGHT_TOP_VIEW:
                # Right foot top view - get length and width
                detector = TopViewFootDetector(normalized_img, debug=self.debug)
                top_keypoints = detector.keypoints()
                result["rightFoot"].update(geom.metrics_from_top_view(top_keypoints))

            elif img_idx == self.LEFT_SIDE_VIEW:
                # Left foot inner side view - get arch measurements
                detector = SideViewFootDetector(normalized_img, debug=self.debug)
                side_keypoints = detector.keypoints()
                result["leftFoot"].update(geom.metrics_from_side_view(side_keypoints))

            elif img_idx == self.RIGHT_SIDE_VIEW:
                # Right foot inner side view - get arch measurements
                detector = SideViewFootDetector(normalized_img, debug=self.debug)
                side_keypoints = detector.keypoints()
                result["rightFoot"].update(geom.metrics_from_side_view(side_keypoints))

        if self.debug:
            logger.debug("Result JSON: %s", json.dumps(result, indent=2))
        return result

    # ------------------------------------------------------------------
    # Private helpers
    # ------------------------------------------------------------------

    def _load_and_validate(self, path: Path) -> np.ndarray:
        if not path.exists():
            raise FileNotFoundError(path)
        img = cv2.imread(str(path))
        if img is None:
            raise ValueError(f"Could not read image: {path}")

        # Detect orientation
        h, w = img.shape[:2]
        orientation = self._detect_orientation(w, h)

        # Check dimensions based on orientation
        if orientation == ORIENTATION_LANDSCAPE:
            expected_w, expected_h = (
                STANDARD_LANDSCAPE_WIDTH_PX,
                STANDARD_LANDSCAPE_HEIGHT_PX,
            )
        else:  # portrait
            expected_w, expected_h = (
                STANDARD_PORTRAIT_WIDTH_PX,
                STANDARD_PORTRAIT_HEIGHT_PX,
            )

        if (w, h) != (expected_w, expected_h):
            logger.warning(
                "Image %s dims %sx%s px differ from standard %sx%s px for %s orientation",
                path.name,
                w,
                h,
                expected_w,
                expected_h,
                orientation,
            )

        return img

    @staticmethod
    def _detect_orientation(width: int, height: int) -> str:
        """Detect if an image is in portrait or landscape orientation."""
        if width > height:
            return ORIENTATION_LANDSCAPE
        return ORIENTATION_PORTRAIT

    def normalize_orientation(
        self, img: np.ndarray, target_orientation: str = ORIENTATION_LANDSCAPE
    ) -> np.ndarray:
        """Rotate the image if needed to match the target orientation.

        Args:
            img: The input image
            target_orientation: Desired orientation ('landscape' or 'portrait')

        Returns:
            Rotated image if needed, original image otherwise
        """
        h, w = img.shape[:2]
        current_orientation = self._detect_orientation(w, h)

        # If orientations already match, return original
        if current_orientation == target_orientation:
            return img

        # Otherwise, rotate 90 degrees to convert between landscape and portrait
        if self.debug:
            logger.info(
                "Rotating image from %s to %s", current_orientation, target_orientation
            )

        # Rotate 90 degrees
        # cv2 constants for rotation: 0=90deg, 1=180deg, 2=270deg
        return cv2.transpose(cv2.flip(img, 1))


###############################################################################
# Command‑line interface (demo)                                               #
###############################################################################


def _build_cli() -> None:  # pragma: no cover
    import argparse

    parser = argparse.ArgumentParser(description="EigenFit Foot Measurement")
    parser.add_argument(
        "images",
        nargs=4,
        help=(
            "Paths to four specific foot images:\n"
            "1. Left foot from top view\n"
            "2. Right foot from top view\n"
            "3. Left foot inner side view (for arch)\n"
            "4. Right foot inner side view (for arch)"
        ),
    )
    parser.add_argument("--debug", action="store_true", help="Verbose logging")
    args = parser.parse_args()

    session = EigenFitSession(args.images, debug=args.debug)
    print(json.dumps(session.run(), indent=2))


if __name__ == "__main__":  # pragma: no cover
    _build_cli()
