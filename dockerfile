# ---- Base image -------------------------------------------------
FROM mambaorg/micromamba:1.5.8

# Let RUN/C<PERSON> automatically see the conda env
ARG MAMBA_DOCKERFILE_ACTIVATE=1

# ---- Re-create your env inside the image ------------------------
COPY environment.yml /tmp/environment.yml
RUN micromamba create -y -n NDF -f /tmp/environment.yml \
    && micromamba clean --all --yes

# ---- Copy your code & set working dir ---------------------------
WORKDIR /app
COPY demo.py .

# ---- Default command --------------------------------------------
C<PERSON> ["micromamba", "run", "-n", "NDF", "python", "demo.py"]