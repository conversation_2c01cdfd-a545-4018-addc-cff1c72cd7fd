# ---- Base image -------------------------------------------------
FROM python:3.10-slim

# ---- Install system dependencies --------------------------------
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    git \
    curl \
    build-essential \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# ---- Set working directory --------------------------------------
WORKDIR /app

# ---- Install PyTorch first --------------------------------------
RUN pip install --no-cache-dir torch>=1.9.0 torchvision>=0.10.0

# ---- Install detectron2 from wheels -----------------------------
RUN python -m pip install 'git+https://github.com/facebookresearch/detectron2.git'

# ---- Copy requirements and install remaining dependencies -------
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# ---- Copy application files -------------------------------------
COPY eigenfit_image_analysis_template.py .
COPY app.py .
COPY models/ ./models/
COPY Feet_Measurements/ ./Feet_Measurements/

# ---- Create directories for uploads and results -----------------
RUN mkdir -p /app/uploads /app/results

# ---- Expose port ------------------------------------------------
EXPOSE 8000

# ---- Health check -----------------------------------------------
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# ---- Default command --------------------------------------------
CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]