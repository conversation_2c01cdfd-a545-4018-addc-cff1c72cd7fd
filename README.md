# EigenFit Foot Measurement System

A computer vision-based system for measuring foot dimensions from smartphone images using AI and machine learning techniques.

## Overview

This system extracts physical foot measurements from a specific set of four smartphone images:

1. **Left foot from top view** - for length and width measurements
2. **Right foot from top view** - for length and width measurements
3. **Left foot inner side view** - for arch calculations
4. **Right foot inner side view** - for arch calculations

The system uses A4 paper as a reference scale in each image to derive millimeter-per-pixel calibration.

## Features

- **Automated A4 Paper Detection**: Uses computer vision to detect and calibrate measurements using A4 paper reference
- **Foot Segmentation**: Advanced image processing to isolate foot regions from background
- **Keypoint Detection**: Identifies critical foot landmarks for accurate measurements
- **Multi-view Processing**: Combines top and side view measurements for comprehensive foot analysis
- **RESTful API**: FastAPI-based web service for easy integration
- **Docker Support**: Containerized deployment for consistent environments
- **Orientation Handling**: Automatically detects and handles portrait/landscape orientations

## Project Structure

```
├── eigenfit_image_analysis_template.py  # Core measurement algorithms
├── app.py                               # FastAPI web service
├── demo.py                              # Standalone demo script
├── dockerfile                           # Container definition
├── requirements_clean.txt               # Python dependencies
├── environment.yml                      # Conda environment (legacy)
├── models/                              # Pre-trained models
│   ├── config.yml                       # Model configuration
│   └── model.pth                        # Layout detection model weights
├── Feet_Measurements/                   # Additional measurement utilities
│   ├── __init__.py
│   ├── geometric.py                     # Geometric processing
│   ├── image_processor.py               # Image preprocessing
│   ├── pipeline.py                      # Processing pipeline
│   └── utils.py                         # Utility functions
├── ExampleImages/                       # Sample test images
├── images/                              # Additional test images
├── detectron2_repo/                     # Detectron2 framework (submodule)
└── RnD_Height_Width_Detector.ipynb     # Research notebook
```

## Installation

### Option 1: Using Docker (Recommended)

1. **Build the Docker image:**

   ```bash
   docker build -t eigenfit-foot-measurement .
   ```

2. **Run the container:**

   ```bash
   # Basic run
   docker run -p 8000:8000 eigenfit-foot-measurement

   # Run with volume mounting for persistent data
   docker run -p 8000:8000 -v $(pwd)/data:/app/data eigenfit-foot-measurement

   # Run in background
   docker run -d -p 8000:8000 --name eigenfit-api eigenfit-foot-measurement
   ```

3. **Docker Compose (Alternative):**

   ```yaml
   # docker-compose.yml
   version: "3.8"
   services:
     eigenfit-api:
       build: .
       ports:
         - "8000:8000"
       volumes:
         - ./data:/app/data
       environment:
         - LOG_LEVEL=INFO
       restart: unless-stopped
   ```

   Run with: `docker-compose up -d`

4. **Access the API:**

   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health
   - Interactive API: http://localhost:8000/redoc

5. **Container Management:**

   ```bash
   # View logs
   docker logs eigenfit-api

   # Stop container
   docker stop eigenfit-api

   # Remove container
   docker rm eigenfit-api

   # View running containers
   docker ps
   ```

### Option 2: Local Installation

1. **Clone the repository:**

   ```bash
   git clone <repository-url>
   cd ImageAnalysis
   ```

2. **Install dependencies:**

   ```bash
   pip install -r requirements_clean.txt
   ```

3. **Download model files:**

   - Ensure `models/config.yml` and `models/model.pth` are present
   - These contain the pre-trained layout detection model

4. **Run the API server:**
   ```bash
   uvicorn app:app --host 0.0.0.0 --port 8000
   ```

## Usage

### API Endpoints

#### POST /measure

Upload 4 images for foot measurement.

**Request:**

- Content-Type: `multipart/form-data`
- Body: 4 image files in the specified order

**Response:**

```json
{
  "leftFoot": {
    "heelToToeLength": 245.5,
    "footMaxWidth": 95.2,
    "archLength": 180.3,
    "archHeight": 25.8
  },
  "rightFoot": {
    "heelToToeLength": 246.1,
    "footMaxWidth": 94.8,
    "archLength": 181.0,
    "archHeight": 26.2
  }
}
```

#### GET /health

Health check endpoint for monitoring.

#### GET /

API information and available endpoints.

### Command Line Usage

```bash
python eigenfit_image_analysis_template.py \
  left_top.jpg right_top.jpg left_side.jpg right_side.jpg
```

### Demo Script

```bash
python demo.py
```

Processes all images in the `./images` directory.

## Image Requirements

### Image Specifications

- **Format**: JPEG, PNG, or WebP
- **Resolution**: Minimum 1536x2048 pixels (portrait) or 2048x1536 pixels (landscape)
- **Quality**: High quality, well-lit images
- **Background**: Contrasting background (preferably white A4 paper)

### Image Setup Guidelines

1. **A4 Paper Reference**: Place foot on white A4 paper for scale calibration
2. **Lighting**: Ensure even, bright lighting without harsh shadows
3. **Camera Position**:
   - Top view: Camera directly above foot
   - Side view: Camera at foot level, perpendicular to foot
4. **Foot Position**: Foot should be flat and relaxed
5. **Frame Coverage**: Ensure entire foot and A4 paper are visible

## Technical Details

### Core Components

1. **Calibrator**: Detects A4 paper and calculates mm-per-pixel scale
2. **TopViewFootDetector**: Extracts foot keypoints from top view images
3. **SideViewFootDetector**: Extracts arch measurements from side view images
4. **FootGeometry**: Converts pixel measurements to millimeters
5. **EigenFitSession**: Orchestrates the complete measurement pipeline

### Dependencies

- **Computer Vision**: OpenCV, scikit-image
- **Deep Learning**: PyTorch, Detectron2
- **Layout Analysis**: LayoutParser
- **Geometry**: Shapely, NumPy, SciPy
- **Web Framework**: FastAPI, Uvicorn
- **Image Processing**: Pillow

### Model Information

The system uses a pre-trained Detectron2 model for layout detection to identify A4 paper regions in images. The model is based on Faster R-CNN with ResNet-50 backbone.

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black .
```

### Adding New Features

1. Extend the appropriate detector classes in `eigenfit_image_analysis_template.py`
2. Update the measurement pipeline in `EigenFitSession.run()`
3. Add corresponding tests
4. Update API documentation

## Troubleshooting

### Common Issues

1. **"No A4 paper detected"**: Ensure A4 paper is clearly visible and well-lit
2. **"No foot contour found"**: Check image quality and foot positioning
3. **Import errors**: Verify all dependencies are installed correctly
4. **Model loading errors**: Ensure model files are present in `models/` directory

### Debug Mode

Enable debug logging by setting `debug=True` in `EigenFitSession`:

```python
session = EigenFitSession(img_paths, debug=True)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions, please [create an issue](link-to-issues) in the repository.
