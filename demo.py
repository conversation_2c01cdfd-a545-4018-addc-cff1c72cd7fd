import os
from pathlib import Path
import logging
from typing import Tuple, Optional
from shapely.geometry import Polygon, LineString
import cv2
import numpy as np
import layoutparser as lp


# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


def order_points(pts: np.ndarray) -> np.ndarray:
    """
    Order 4 points in the order: top-left, top-right, bottom-right, bottom-left.
    """
    rect = np.zeros((4, 2), dtype="float32")
    s = pts.sum(axis=1)
    diff = np.diff(pts, axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    return rect


class ImagePreprocessor:
    """
    Loads and preprocesses an image to isolate the A4 page region using a layout detection model.
    """
    def __init__(self, model: lp.models.Detectron2LayoutModel) -> None:
        self.model = model

    def load_and_crop(self, image_path: Path) -> np.ndarray:
        img_bgr = cv2.imread(str(image_path))
        if img_bgr is None:
            raise FileNotFoundError(f"Image not found: {image_path}")
        img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)

        layout = self.model.detect(img_rgb)
        pages = [block for block in layout._blocks]
        if not pages:
            raise RuntimeError("No page detected in layout")
        page = max(pages, key=lambda x: x.score)
        x1, y1, x2, y2 = map(int, page.coordinates)

        # Add padding around detected page
        pad = int(0.02 * max(img_bgr.shape[:2]))
        x1, y1 = max(0, x1 - pad), max(0, y1 - pad)
        x2 = min(img_bgr.shape[1], x2 + pad)
        y2 = min(img_bgr.shape[0], y2 + pad)

        roi = img_bgr[y1:y2, x1:x2]
        return roi


class PageDetector:
    """
    Further refines the page contour using color thresholding and edge detection.
    """
    def detect_contour(self, roi: np.ndarray) -> np.ndarray:
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        mask = cv2.inRange(hsv, (0, 0, 125), (180, 30, 255))
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
        mask_closed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_close)
        edges = cv2.Canny(mask_closed, 100, 150)

        kernel_close2 = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close2)

        contours, _ = cv2.findContours(edges_closed, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise RuntimeError("No contours found in page detection")

        # Select the largest contour
        largest = max(contours, key=cv2.contourArea)
        return largest


class QuadrilateralApproximator:
    """
    Approximates a contour to a quadrilateral, based on convex hull and polyDP.
    """
    @staticmethod
    def get_quadrilateral(pts: np.ndarray, max_iters: int = 100, eps_scale: float = 0.01) -> np.ndarray:
        pts2d = pts.reshape(-1, 2)
        hull = cv2.convexHull(pts2d)
        peri = cv2.arcLength(hull, True)

        best_quad: Optional[np.ndarray] = None
        best_area = 0.0
        eps_start: float = 0.001
        eps_end: float = 0.1
        # Binary search for optimal epsilon
        low, high = int(eps_start * peri), int(eps_end * peri)
        while low <= high:
            eps = (low + high) // 2
            approx = cv2.approxPolyDP(hull, eps, True)
            if len(approx) == 4:
                area = cv2.contourArea(approx)
                if area > best_area:
                    best_area = area
                    best_quad = approx.reshape(-1, 2)
                low = eps + 1  # Prefer smaller epsilon
            else:
                high = eps - 1

        # for i in range(1, max_iters + 1):
        #     eps = eps_scale * i * peri
        #     approx = cv2.approxPolyDP(hull, eps, True)
        #     if len(approx) == 4:
        #         area = cv2.contourArea(approx)
        #         if area > best_area:
        #             best_area = area
        #             best_quad = approx.reshape(-1, 2)
        if best_quad is None:
            # fallback using extreme points
            xs, ys = pts2d[:, 0], pts2d[:, 1]
            best_quad = np.array([
                pts2d[np.argmin(xs)],
                pts2d[np.argmax(xs)],
                pts2d[np.argmin(ys)],
                pts2d[np.argmax(ys)],
            ], dtype="float32")
        return best_quad


class A4Warper:
    """
    Warps the detected quadrilateral to a standard A4 size and computes mm-to-pixel ratios.
    """
    A4_WIDTH_MM = 210.0
    A4_HEIGHT_MM = 297.0

    def warp(self, roi: np.ndarray, quad: np.ndarray) -> Tuple[Tuple[float, float], np.ndarray]:
        src = order_points(quad.astype("float32"))

        # compute dimensions
        width_top = np.linalg.norm(src[1] - src[0])
        width_bottom = np.linalg.norm(src[2] - src[3])
        max_w = int(max(width_top, width_bottom))

        height_left = np.linalg.norm(src[3] - src[0])
        height_right = np.linalg.norm(src[2] - src[1])
        max_h = int(max(height_left, height_right))

        # enforce A4 aspect ratio if necessary
        desired_aspect = self.A4_HEIGHT_MM / self.A4_WIDTH_MM
        if abs(max_h / max_w - desired_aspect) > 0.01:
            max_h = int(max_w * desired_aspect)

        dst = np.array([
            [0, 0],
            [max_w - 1, 0],
            [max_w - 1, max_h - 1],
            [0, max_h - 1]
        ], dtype="float32")

        matrix = cv2.getPerspectiveTransform(src, dst)
        warped = cv2.warpPerspective(roi, matrix, (max_w, max_h))

        mm_per_px_x = self.A4_WIDTH_MM / float(max_w)
        mm_per_px_y = self.A4_HEIGHT_MM / float(max_h)
        return (mm_per_px_x, mm_per_px_y), warped


class FootSegmenter:
    """
    Segments the foot contour from the warped A4 image.
    """
    def segment(self, warped: np.ndarray) -> np.ndarray:
        hsv = cv2.cvtColor(warped, cv2.COLOR_BGR2HSV)
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
        closed = cv2.morphologyEx(hsv, cv2.MORPH_CLOSE, kernel)
        edges = cv2.Canny(closed, 100, 150)
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        contours, _ = cv2.findContours(edges_closed, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise RuntimeError("No foot contour detected")

        # choose the largest contour as the foot
        foot = max(contours, key=cv2.contourArea)
        return foot


class FootMeasurementPipeline:
    """
    Runs the full pipeline over a directory of images to measure foot length.
    """
    def __init__(self, model: lp.models.Detectron2LayoutModel, image_dir: Path) -> None:
        self.preprocessor = ImagePreprocessor(model)
        self.page_detector = PageDetector()
        self.quadr_approx = QuadrilateralApproximator()
        self.warper = A4Warper()
        self.segmenter = FootSegmenter()
        self.image_dir = image_dir

    def run(self) -> None:
        for image_file in self.image_dir.iterdir():
            if not image_file.is_file():
                continue
            logging.info(f"Processing image: {image_file.name}")
            try:
                roi = self.preprocessor.load_and_crop(image_file)
                page_contour = self.page_detector.detect_contour(roi)
                quad = self.quadr_approx.get_quadrilateral(page_contour)
                (mm_px_x, mm_px_y), warped = self.warper.warp(roi, quad)
                foot_contour = self.segmenter.segment(warped)
                # compute foot length
                length_px = np.max(foot_contour, axis=0)[0][1]
                logging.info(f"Foot length for {image_file.name}: {length_px:.3f} mm")
                length_mm = length_px * ((mm_px_x + mm_px_y) / 2.0)
                logging.info(f"Foot length for {image_file.name}: {length_mm:.2f} mm")

            except Exception as e:
                logging.error(f"Failed to process {image_file.name}: {e}")



if __name__ == "__main__":
    model = lp.models.Detectron2LayoutModel(
        config_path='config.yml',
        model_path='model.pth',
        extra_config=['MODEL.ROI_HEADS.SCORE_THRESH_TEST', 0.8],
        label_map={0: 'Page'}
    )
    pipeline = FootMeasurementPipeline(model, Path('./images'))
    pipeline.run()
