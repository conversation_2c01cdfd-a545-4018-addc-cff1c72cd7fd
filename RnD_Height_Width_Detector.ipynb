!git clone https://github.com/facebookresearch/detectron2 detectron2_repo
!pip install -e detectron2_repo

# download config and weights files
import urllib.request
urllib.request.urlretrieve("https://www.dropbox.com/s/f3b12qc4hc0yh4m/config.yml?dl=1", "config.yml")
urllib.request.urlretrieve("https://www.dropbox.com/s/dgy9c10wykk4lq4/model_final.pth?dl=1", "model.pth")

# load model using local files

!pip install layoutparser

import cv2
import numpy as np
import layoutparser as lp
from matplotlib import pyplot as plt
import os
from google.colab.patches import cv2_imshow

import os
from pathlib import Path
import logging
from typing import Tuple, Optional, List
import cv2
import numpy as np
import layoutparser as lp
from shapely.geometry import Polygon, LineString

class ImagePreprocessor:
    """Improved with adaptive thresholding and better padding calculation"""
    def __init__(self, model: lp.models.Detectron2LayoutModel) -> None:
        self.model = model

    def load_and_crop(self, image_path: Path) -> np.ndarray:
        img_bgr = cv2.imread(str(image_path))
        if img_bgr is None:
            raise FileNotFoundError(f"Image not found: {image_path}")

        # Convert to grayscale for better layout detection
        img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)

        layout = self.model.detect(img_rgb)
        pages = pages = [block for block in layout._blocks]

        if not pages:
            raise RuntimeError("No page detected in layout")

        page = max(pages, key=lambda x: x.score)
        x1, y1, x2, y2 = map(int, page.coordinates)

        # Dynamic padding based on image resolution
        pad = int(0.02 * max(img_bgr.shape[:2]))
        pad_x = int(pad * (img_bgr.shape[1] / img_bgr.shape[0]))
        pad_y = int(pad * (img_bgr.shape[0] / img_bgr.shape[1]))

        roi = img_bgr[max(0, y1-pad_y):min(img_bgr.shape[0], y2+pad_y),
                      max(0, x1-pad_x):min(img_bgr.shape[1], x2+pad_x)]
        return roi



class QuadrilateralApproximator:
    """Improved with binary search and aspect ratio validation"""
    @staticmethod
    def get_quadrilateral(pts: np.ndarray,
                         eps_start: float = 0.001,
                         eps_end: float = 0.1) -> np.ndarray:
        pts2d = pts.reshape(-1, 2)
        hull = cv2.convexHull(pts2d)
        peri = cv2.arcLength(hull, True)

        best_quad = None
        best_area = 0
        low, high = int(eps_start * peri), int(eps_end * peri)

        # Binary search for optimal epsilon
        while low <= high:
            eps = (low + high) // 2
            approx = cv2.approxPolyDP(hull, eps, True)
            if len(approx) == 4:
                area = cv2.contourArea(approx)
                if area > best_area:
                    best_area = area
                    best_quad = approx.reshape(-1, 2)
                low = eps + 1  # Prefer tighter approximation
            else:
                high = eps - 1

        # Validate aspect ratio if quadrilateral found
        if best_quad is not None:
            src = order_points(best_quad)
            width = np.linalg.norm(src[1] - src[0])
            height = np.linalg.norm(src[2] - src[1])
            aspect_ratio = height / width
            if not (1.3 < aspect_ratio < 1.5):  # A4 aspect ratio range
                best_quad = None

        # Fallback with aspect ratio correction
        if best_quad is None:
            xs, ys = pts2d[:, 0], pts2d[:, 1]
            extreme_points = np.array([
                [np.min(xs), np.min(ys)],
                [np.max(xs), np.min(ys)],
                [np.max(xs), np.max(ys)],
                [np.min(xs), np.max(ys)]
            ], dtype="float32")

            # Adjust points to match A4 aspect ratio
            width = extreme_points[1][0] - extreme_points[0][0]
            target_height = width * 297/210
            extreme_points[2][1] = extreme_points[0][1] + target_height
            extreme_points[3][1] = extreme_points[0][1] + target_height
            best_quad = extreme_points

        return best_quad

class A4Warper:
    """Improved with homography validation and dynamic scaling"""
    A4_WIDTH_MM = 210.0
    A4_HEIGHT_MM = 297.0

    def warp(self, roi: np.ndarray, quad: np.ndarray) -> Tuple[Tuple[float, float], np.ndarray]:
        src = order_points(quad.astype("float32"))

        # Calculate actual dimensions from quadrilateral
        width = np.mean([np.linalg.norm(src[1]-src[0]),
                         np.linalg.norm(src[2]-src[3])])
        height = np.mean([np.linalg.norm(src[3]-src[0]),
                          np.linalg.norm(src[2]-src[1])])

        # Calculate scaling factors
        scale_x = self.A4_WIDTH_MM / width
        scale_y = self.A4_HEIGHT_MM / height
        mm_per_px = (scale_x + scale_y) / 2  # Average scaling factor

        # Create destination points with correct aspect ratio
        dst_width = int(width * scale_x)
        dst_height = int(height * scale_y)

        dst = np.array([
            [0, 0],
            [dst_width - 1, 0],
            [dst_width - 1, dst_height - 1],
            [0, dst_height - 1]
        ], dtype="float32")

        # Use RANSAC for better homography estimation
        matrix, _ = cv2.findHomography(src, dst, cv2.RANSAC, 5.0)
        warped = cv2.warpPerspective(roi, matrix, (dst_width, dst_height))

        return (mm_per_px, mm_per_px), warped


class FootSegmenter:
    """Improved with GrabCut and morphological refinement"""
    def segment(self, warped: np.ndarray) -> np.ndarray:
        # Initial mask using Otsu's thresholding
        gray = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)
        _, mask = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Find largest contour for GrabCut initialization
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise RuntimeError("No initial contours found")

        largest_contour = max(contours, key=cv2.contourArea)
        x,y,w,h = cv2.boundingRect(largest_contour)

        # Initialize GrabCut
        bgd_model = np.zeros((1,65), np.float64)
        fgd_model = np.zeros((1,65), np.float64)
        grabcut_mask = np.zeros(warped.shape[:2], np.uint8)
        grabcut_mask[y:y+h, x:x+w] = cv2.GC_PR_FGD

        cv2.grabCut(warped, grabcut_mask, None, bgd_model, fgd_model,
                  5, cv2.GC_INIT_WITH_MASK)

        # Refine mask
        foot_mask = np.where((grabcut_mask == cv2.GC_FGD) |
                          (grabcut_mask == cv2.GC_PR_FGD), 255, 0).astype("uint8")

        # Morphological cleanup
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15,15))
        foot_mask = cv2.morphologyEx(foot_mask, cv2.MORPH_CLOSE, kernel)
        foot_mask = cv2.morphologyEx(foot_mask, cv2.MORPH_OPEN, kernel)

        return foot_mask



class PageDetector:
    """
    Further refines the page contour using color thresholding and edge detection.
    """
    def detect_contour(self, roi: np.ndarray) -> np.ndarray:
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        mask = cv2.inRange(hsv, (0, 0, 125), (180, 30, 255))
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
        mask_closed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_close)
        edges = cv2.Canny(mask_closed, 100, 150)

        kernel_close2 = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close2)

        contours, _ = cv2.findContours(edges_closed, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise RuntimeError("No contours found in page detection")

        # Select the largest contour
        largest = max(contours, key=cv2.contourArea)
        return largest



def order_points(pts: np.ndarray) -> np.ndarray:
    """
    Order 4 points in the order: top-left, top-right, bottom-right, bottom-left.
    """
    rect = np.zeros((4, 2), dtype="float32")
    s = pts.sum(axis=1)
    diff = np.diff(pts, axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    return rect

    model = lp.models.Detectron2LayoutModel(
        config_path='config.yml',
        model_path='model.pth',
        extra_config=['MODEL.ROI_HEADS.SCORE_THRESH_TEST', 0.8],
        label_map={0: 'Page'}
    )

preprocessor = ImagePreprocessor(model)
page_detector = PageDetector()
quadr_approx = QuadrilateralApproximator()
warper = A4Warper()
segmenter = FootSegmenter()
image_dir = "./images"

image_file = '/content/images/20250327_082125846_iOS.webp'

roi = preprocessor.load_and_crop(image_file)
cv2_imshow(roi)

page_contour = page_detector.detect_contour(roi)
temp = roi.copy()
cv2.drawContours(temp, [page_contour], -1, (0, 255, 0), 2)
cv2_imshow(temp)

# import cv2
# import numpy as np

# img = roi.copy()
# hull = cv2.convexHull(page_contour)

# # --- you already have these: ---
# # combined_cnt = your contour of page+foot
# # hull         = cv2.convexHull(combined_cnt)
# # img          = your original image

# # (1) build the “page” mask (filled hull)
# mask_page = np.zeros(img.shape[:2], np.uint8)
# cv2.drawContours(mask_page, [hull], -1, 255, thickness=-1)

# # (2) build the “page minus foot” mask (filled combined contour)
# mask_all = np.zeros_like(mask_page)
# cv2.drawContours(mask_all, [page_contour], -1, 255, thickness=-1)

# # (3) subtract: full page minus (page minus foot) = foot alone
# mask_foot = cv2.subtract(mask_page, mask_all)
# _, mask_foot = cv2.threshold(mask_foot, 1, 255, cv2.THRESH_BINARY)

# # (4) clean up / remove tiny specks if you need
# kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
# mask_foot = cv2.morphologyEx(mask_foot, cv2.MORPH_OPEN, kernel)

# # (5) find the foot contour
# foot_cnts, _ = cv2.findContours(mask_foot, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
# foot_cnt = max(foot_cnts, key=cv2.contourArea)

# # (6) measure in pixels
# x,y,w,h = cv2.boundingRect(foot_cnt)
# print(f"Foot width  = {w}px")
# print(f"Foot length = {h}px")


quad = quadr_approx.get_quadrilateral(page_contour)
temp = roi.copy()
pts = quad.reshape((-1, 1, 2))

# 3. Draw the quadrilateral
cv2.polylines(temp,
              [pts],         # list of polygons
              isClosed=True, # connect last point back to first
              color=(0,255,0), # BGR: green
              thickness=3)

# 4. Show it
cv2_imshow(temp)


import numpy as np
import cv2

# Your quad corners (in order)
pts = np.array([
    [445,  681],
    [1167,  687],
    [1158, 1723],
    [ 436, 1872],
], dtype=np.int32)

# 1. Enumerate the 4 edges
edges = [(pts[i], pts[(i+1)%4]) for i in range(4)]

# 2. Compute vector, length, unit-vector for each
edge_data = []
for A, B in edges:
    v = B - A
    length = np.hypot(*(v))
    u = v / (length + 1e-8)
    edge_data.append({'A':A, 'B':B, 'v':v, 'u':u, 'length':length})

# 3. Sort by length
edge_data.sort(key=lambda e: e['length'], reverse=True)
longest = edge_data[0]
candidates = edge_data[1:]
perp     = min(candidates, key=lambda e: abs(np.dot(e['u'], longest['u'])))

# 4. Find the shared corner (origin) between those two edges
set_long = {tuple(longest['A']), tuple(longest['B'])}
set_perp = {tuple(perp['A']),     tuple(perp['B'])}
common_pt = np.array(set_long.intersection(set_perp).pop())

# 5. Identify the “far” endpoints of each
def other_point(edge, common):
    return edge['A'] if np.array_equal(edge['A'], common) == False else edge['B']

P_long = other_point(longest, common_pt)
P_perp = other_point(perp,   common_pt)

# 6. Build the fourth corner by vector addition
corner4 = P_long + (P_perp - common_pt)

# 7. Assemble rectangle in order
rect = np.array([common_pt, P_long, corner4, P_perp], dtype=np.int32)

print("Rectangle corners:")
for p in rect:
    print(tuple(p))

# 8. (Optional) Visualize on an image
temp = roi.copy()
cv2.polylines(temp, [rect.reshape(-1,1,2)], True, (0,255,0), 3)
cv2_imshow(temp)


A4_WIDTH_MM = 210
A4_HEIGHT_MM = 297

src = order_points(rect.astype("float32"))
width = np.mean([np.linalg.norm(src[1]-src[0]), np.linalg.norm(src[2]-src[3])])
height = np.mean([np.linalg.norm(src[3]-src[0]), np.linalg.norm(src[2]-src[1])])

scale_x = A4_WIDTH_MM / width
scale_y = A4_HEIGHT_MM / height
mm_per_px = (scale_x + scale_y) / 2

dst_width = int(width * scale_x)
dst_height = int(height * scale_y)

dst = np.array([
  [0, 0],
  [dst_width - 1, 0],
  [dst_width - 1, dst_height - 1],
  [0, dst_height - 1]
  ], dtype="float32")

matrix, _ = cv2.findHomography(src, dst, cv2.RANSAC, 5.0)
warped = cv2.warpPerspective(roi, matrix, (dst_width, dst_height))

warped_pts = cv2.perspectiveTransform(page_contour.astype(dtype='float32'), matrix)

cv2_imshow(warped)

# prompt: crop the bottom 30% of the image

# Get the dimensions of the warped image
height, width = warped.shape[:2]

# Calculate the height to crop from the bottom (30%)
crop_height = int(height * 0.30)

# Crop the image
cropped_warped = warped[:height - crop_height, :]

# Display the cropped image
cv2_imshow(cropped_warped)

import cv2
import numpy as np

# warped: your top-down page+foot image
# mm_per_px: your calibration (mm per pixel) from the A4 homography

# 1. Binarize (foot=white, page=black)
gray  = cv2.cvtColor(cropped_warped, cv2.COLOR_BGR2GRAY)
cv2_imshow(gray)
blur  = cv2.GaussianBlur(gray, (15,15), 0)
cv2_imshow(blur)
_, mask = cv2.threshold(blur, 180, 255, cv2.THRESH_BINARY_INV)
cv2_imshow(mask)

# 2. Morphological clean-up
kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3,3))
mask   = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=3)
mask   = cv2.morphologyEx(mask, cv2.MORPH_OPEN,  kernel, iterations=3)

# 3. Find contours and pick the largest
contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
foot_cnt = max(contours, key=cv2.contourArea)

# (optional) visualize
out = warped.copy()
cv2.drawContours(out, [foot_cnt], -1, (0,255,0), 2)
cv2_imshow( mask)
cv2_imshow( out)

tt = cv2.convexHull(foot_cnt)
cv2.polylines(cropped_warped.copy(), [tt.astype(np.int32)], isClosed=True, color=(0,255,0), thickness=2)


cv2.polylines(warped.copy(), [warped_pts.astype(np.int32)], isClosed=True, color=(0,255,0), thickness=2)

# --- assume you already have this from before: ---
# warped        = full A4-warped image (before you did the bottom-30% crop)
# crop_height   = int(warped.shape[0] * 0.30)
# cropped_warped = warped[:warped.shape[0] - crop_height, :]

# 1) re-segment the foot ON THE FULL warped image:
gray_full = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)
blur      = cv2.GaussianBlur(gray_full, (15,15), 0)
_, full_mask = cv2.threshold(blur, 180, 255, cv2.THRESH_BINARY_INV)
# (and do your same MORPH_OPEN/CLOSE cleanup here…)
cnts_full, _ = cv2.findContours(full_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
foot_cnt_full = max(cnts_full, key=cv2.contourArea)

# 2) build a full-size mask & FILL that contour
h_full, w_full = full_mask.shape
mask_full = np.zeros((h_full, w_full), dtype=np.uint8)
cv2.drawContours(mask_full, [foot_cnt_full], -1, 255, thickness=cv2.FILLED)

# 3) crop the mask EXACTLY the same way you cropped the image
h_crop = h_full - crop_height
mask_clip = mask_full[:h_crop, :]

# 4) re-extract the contour from that clipped mask
cnts_clip, _ = cv2.findContours(mask_clip, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
if not cnts_clip:
    raise RuntimeError("No clipped‐mask contour found!")
foot_cnt_clipped = max(cnts_clip, key=cv2.contourArea)

hull =
# 5) DRAW it on your second image:
out = cropped_warped.copy()
cv2.polylines(out, [foot_cnt_clipped], isClosed=True, color=(0,0,255), thickness=2)
cv2_imshow(out)


import numpy as np
import cv2

# --- 1) Flatten your contour and the rect into simple Nx2 / 4×2 arrays
pts = warped_pts.reshape(-1,2)           # shape=(N,2)
rect = dst.astype(np.float32)            # shape=(4,2)

# --- 2) Build the 4 straight page-edges as line segments
lines = []
for i in range(4):
    A = rect[i]
    B = rect[(i+1)%4]
    lines.append((A,B))

def point_line_dist(pt, AB):
    A, B = AB
    v = B - A
    # project pt onto AB (clamped to the segment)
    t = np.dot(pt-A, v) / (np.dot(v,v) + 1e-8)
    t = np.clip(t, 0.0, 1.0)
    proj = A + t*v
    return np.linalg.norm(pt - proj)

# --- 3) Compute each pt's minimum distance to ANY page-edge
dists = np.array([[point_line_dist(p,edge) for edge in lines] for p in pts])
min_dist = dists.min(axis=1)

# --- 4) Classify: “on the page border” vs “foot”
px_thresh = 2.5   # → anything closer than ~2.5px to an edge is page-border
is_page = min_dist < px_thresh
# reshape back into contours for cv2
page_border_cnt = pts[is_page].reshape(-1,1,2).astype(np.int32)
foot_cnt         = pts[~is_page].reshape(-1,1,2).astype(np.int32)

# --- 5) (Optional) visualize your split
viz = warped.copy()
# cv2.drawContours(viz, [page_border_cnt], -1, (255,0,0), 2)   # blue = page edges
cv2.drawContours(viz, [foot_cnt],         -1, (  0,255,0), 2)   # green = foot


hull = cv2.convexHull(foot_cnt)
cv2.drawContours(viz, [hull], -1, 255, thickness=-1)
cv2_imshow(viz);

# # --- 6) Measure length & width from the foot contour
# #    Option A: minAreaRect
# rect2 = cv2.minAreaRect(foot_cnt)
# (_, _), (w_px, h_px), _ = rect2
# length_px = max(w_px, h_px)
# width_px  = min(w_px, h_px)

# #    Option B: axis-aligned bounding-box (if your foot is roughly vertical)
# xs = foot_cnt[:,:,0].flatten()
# ys = foot_cnt[:,:,1].flatten()
# length_px2 = ys.max() #- ys.min()
# width_px2  = xs.max() - xs.min()

# # --- 7) Convert to mm
# length_mm = length_px #* mm_per_px
# width_mm  = width_px #* mm_per_px

# print(f"Length ≃ {length_mm:.1f} mm")
# print(f"Width  ≃ {width_mm:.1f} mm")


import numpy as np
import cv2

# 1) build our 4 exact page-edges as infinite lines, but we’ll only
#    classify if you’re within px_thresh of one of them
rect = dst.astype(np.float32)
edges = [
    (rect[0], rect[1]),  # top
    (rect[1], rect[2]),  # right
    (rect[2], rect[3]),  # bottom
    (rect[3], rect[0]),  # left
]

def point_line_dist(pt, AB):
    A, B = AB
    v = B - A
    t = np.dot(pt - A, v) / (np.dot(v, v) + 1e-8)
    t = np.clip(t, 0.0, 1.0)
    proj = A + t * v
    return np.linalg.norm(pt - proj)

# 2) compute min distance of each contour‐point to any page‐edge
dists = np.array([[point_line_dist(p, e) for e in edges] for p in pts])
min_d  = dists.min(axis=1)
px_thresh     = 2.5    # “within 2½ px” of an edge
is_edge_close = min_d < px_thresh

# 3) find all *contiguous* runs of edge-close points
runs = []
start = None
N = len(is_edge_close)
for i in range(N+1):
    m = is_edge_close[i%N] if i<N else False
    if m and start is None:
        start = i
    if (not m or i==N) and start is not None:
        end = i-1
        length = end-start+1
        runs.append((start, end, length))
        start = None

# 4) keep only the LONG runs as true “page border”
min_run_len = int(0.5 * rect[1,0])  # e.g. half your page-width in px
page_pts = np.zeros(N, bool)
for s,e,l in runs:
    if l >= min_run_len:
        # mark every index in that run (and its successor) as page
        for j in range(s, e+1):
            page_pts[j%N] = True
            page_pts[(j+1)%N] = True

# 5) everything else is foot
foot_pts = pts[~page_pts]

# --- visualize ---
out = cv2.cvtColor((warped*0).astype(np.uint8), cv2.COLOR_GRAY2BGR)  # black canvas
cv2.polylines(out, [pts.reshape(-1,1,2).astype(np.int32)],
              True, (255,255,255), 1)            # full contour in white
cv2.polylines(out, [foot_pts.reshape(-1,1,2).astype(np.int32)],
              True, (0,255,0), 2)                # foot only in green
cv2_imshow(out)

# 6) measure length + width from foot_pts
#    length = max y  – min y
y = foot_pts[:,1]
length_px = y.max() - y.min()
length_mm = length_px * mm_per_px

#    width = max x – min x, *optionally* restrict to the top 75% of the foot
x = foot_pts[:,0]
mask = y < (y.min() + 0.75*(y.max()-y.min()))
width_px = x[mask].max() - x[mask].min()
width_mm  = width_px * mm_per_px

print(f"Length ≃ {length_mm:.1f} mm,  Width ≃ {width_mm:.1f} mm")


# --- visualize ---
# Create a black canvas with the same height and width as 'warped' and 3 channels (BGR)
out = np.zeros_like(warped)
cv2.polylines(out, [pts.reshape(-1,1,2).astype(np.int32)],
              True, (255,255,255), 1)            # full contour in white
cv2.polylines(out, [foot_pts.reshape(-1,1,2).astype(np.int32)],
              True, (0,255,0), 2)                # foot only in green
cv2_imshow(out)

# 6) measure length + width from foot_pts
#    length = max y  – min y
y = foot_pts[:,1]
length_px = y.max() - y.min()
length_mm = length_px * mm_per_px

#    width = max x – min x, *optionally* restrict to the top 75% of the foot
x = foot_pts[:,0]
mask = y < (y.min() + 0.75*(y.max()-y.min()))
width_px = x[mask].max() - x[mask].min()
width_mm  = width_px * mm_per_px

print(f"Length ≃ {length_mm:.1f} mm,  Width ≃ {width_mm:.1f} mm")

cv2_imshow(foot_pts)

H = dst[2,1]  # page height in px
margin_px = 10

# keep only the “upper”  part of the foot
mask = foot_pts[:,1] < (H - margin_px)
fw_pts = foot_pts[mask]

width_px = fw_pts[:,0].max() - fw_pts[:,0].min()
width_mm = width_px #* mm_per_px
print(f"Width:  {width_mm:.1f} mm")


import cv2
import numpy as np

# warped: your top-down page+foot image
# mm_per_px: your calibration (mm per pixel) from the A4 homography

# 1. Binarize (foot=white, page=black)
gray  = cv2.cvtColor(warped, cv2.COLOR_BGR2GRAY)
cv2_imshow(gray)
blur  = cv2.GaussianBlur(gray, (15,15), 0)
cv2_imshow(blur)
_, mask = cv2.threshold(blur, 170, 255, cv2.THRESH_BINARY_INV)
cv2_imshow(mask)

# 2. Morphological clean-up
kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7,7))
mask   = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
mask   = cv2.morphologyEx(mask, cv2.MORPH_OPEN,  kernel, iterations=2)

# 3. Find contours and pick the largest
contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
foot_cnt = max(contours, key=cv2.contourArea)

# (optional) visualize
out = warped.copy()
cv2.drawContours(out, [foot_cnt], -1, (0,255,0), 2)
cv2_imshow( mask)
cv2_imshow( out)

# 4. Fit rotated box
rect = cv2.minAreaRect(foot_cnt)
# rect = ( (cx,cy), (width, height), angle )
(cx, cy), (w_px, h_px), angle = rect

# identify length vs width
length_px = max(w_px, h_px)
width_px  = min(w_px, h_px)

# 5. Convert to millimeters
length_mm = length_px #* mm_per_px
width_mm  = width_px  #* mm_per_px

print(f"Foot length: {length_mm:.1f} mm")
print(f"Foot width:  {width_mm:.1f} mm")


img = warped.copy()
mask_page = np.zeros(img.shape[:2], np.uint8)
cv2.drawContours(mask_page, [hull], -1, 255, thickness=-1)

# (2) build the “page minus foot” mask (filled combined contour)
mask_all = np.zeros_like(mask_page)
cv2.drawContours(mask_all, [warped_pts.astype(np.int32)], -1, 255, thickness=-1)

# (3) subtract: full page minus (page minus foot) = foot alone
mask_foot = cv2.subtract(mask_page, mask_all)
_, mask_foot = cv2.threshold(mask_foot, 1, 255, cv2.THRESH_BINARY)

# (4) clean up / remove tiny specks if you need
kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
mask_foot = cv2.morphologyEx(mask_foot, cv2.MORPH_OPEN, kernel)

# (5) find the foot contour
foot_cnts, _ = cv2.findContours(mask_foot, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
foot_cnt = max(foot_cnts, key=cv2.contourArea)

x,y,w,h = cv2.boundingRect(foot_cnt)
print(f"Foot width  = {w}px")
print(f"Foot length = {h}px")


temp_q = quadr_approx.get_quadrilateral(foot_cnt)
temp = warped.copy()
pts = temp_q.reshape((-1, 1, 2))

# 3. Draw the quadrilateral
cv2.polylines(temp,
              [pts.astype(np.int32)],         # list of polygons
              isClosed=True, # connect last point back to first
              color=(0,255,0), # BGR: green
              thickness=3)

# 4. Show it
cv2_imshow(temp)

pts

