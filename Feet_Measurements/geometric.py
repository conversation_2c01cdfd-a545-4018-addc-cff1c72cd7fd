import numpy as np
import cv2
from .utils import order_points


class PageDetector:
    """
    Further refines the page contour using color thresholding and edge detection.
    """
    def detect_contour(self, roi: np.ndarray) -> np.ndarray:
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        mask = cv2.inRange(hsv, (0, 0, 125), (180, 30, 255))
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 50))
        mask_closed = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel_close)
        edges = cv2.Canny(mask_closed, 100, 150)

        kernel_close2 = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        edges_closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close2)

        contours, _ = cv2.findContours(edges_closed, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise RuntimeError("No contours found in page detection")

        # Select the largest contour
        largest = max(contours, key=cv2.contourArea)
        return largest


class QuadrilateralApproximator:
    """Improved with binary search and aspect ratio validation"""
    @staticmethod
    def get_quadrilateral(pts: np.ndarray,
                         eps_start: float = 0.001,
                         eps_end: float = 0.1) -> np.ndarray:
        pts2d = pts.reshape(-1, 2)
        hull = cv2.convexHull(pts2d)
        peri = cv2.arcLength(hull, True)

        best_quad = None
        best_area = 0
        low, high = int(eps_start * peri), int(eps_end * peri)

        # Binary search for optimal epsilon
        while low <= high:
            eps = (low + high) // 2
            approx = cv2.approxPolyDP(hull, eps, True)
            if len(approx) == 4:
                area = cv2.contourArea(approx)
                if area > best_area:
                    best_area = area
                    best_quad = approx.reshape(-1, 2)
                low = eps + 1  # Prefer tighter approximation
            else:
                high = eps - 1

        # Validate aspect ratio if quadrilateral found
        if best_quad is not None:
            src = order_points(best_quad)
            width = np.linalg.norm(src[1] - src[0])
            height = np.linalg.norm(src[2] - src[1])
            aspect_ratio = height / width
            if not (1.3 < aspect_ratio < 1.5):  # A4 aspect ratio range
                best_quad = None

        # Fallback with aspect ratio correction
        if best_quad is None:
            xs, ys = pts2d[:, 0], pts2d[:, 1]
            extreme_points = np.array([
                [np.min(xs), np.min(ys)],
                [np.max(xs), np.min(ys)],
                [np.max(xs), np.max(ys)],
                [np.min(xs), np.max(ys)]
            ], dtype="float32")

            # Adjust points to match A4 aspect ratio
            width = extreme_points[1][0] - extreme_points[0][0]
            target_height = width * 297/210
            extreme_points[2][1] = extreme_points[0][1] + target_height
            extreme_points[3][1] = extreme_points[0][1] + target_height
            best_quad = extreme_points

        return best_quad

    @staticmethod
    def refine_quad_approximation(pts: np.ndarray) -> np.ndarray:
        pts = pts.reshape(-1, 2)
        edges = [(pts[i], pts[(i+1)%4]) for i in range(4)]


        # 2. Compute vector, length, unit-vector for each
        edge_data = []
        for A, B in edges:
            v = B - A
            length = np.hypot(*(v))
            u = v / (length + 1e-8)
            edge_data.append({'A':A, 'B':B, 'v':v, 'u':u, 'length':length})


        # 3. Sort by length
        edge_data.sort(key=lambda e: e['length'], reverse=True)
        longest = edge_data[0]
        candidates = edge_data[1:]
        perp     = min(candidates, key=lambda e: abs(np.dot(e['u'], longest['u'])))



        # 4. Find the shared corner (origin) between those two edges
        set_long = {tuple(longest['A']), tuple(longest['B'])}
        set_perp = {tuple(perp['A']),     tuple(perp['B'])}
        common_pt = np.array(set_long.intersection(set_perp).pop())

        # 5. Identify the “far” endpoints of each
        def other_point(edge, common):
            return edge['A'] if np.array_equal(edge['A'], common) == False else edge['B']

        P_long = other_point(longest, common_pt)
        P_perp = other_point(perp,   common_pt)

        # 6. Build the fourth corner by vector addition
        corner4 = P_long + (P_perp - common_pt)

        # 7. Assemble rectangle in order
        rect = np.array([common_pt, P_long, corner4, P_perp], dtype=np.int32)
        distance_to_plong = np.linalg.norm(P_long - common_pt)
        distance_to_pperp = np.linalg.norm(P_perp - common_pt)


        return rect, 297/distance_to_plong, 210/distance_to_pperp
