import sys
from pathlib import Path
import os
from geometric import PageDete<PERSON>, QuadrilateralApproximator
from image_processor import ImagePreprocessor
from utils import visualise_contours, visualise_quad, crop_image_contour_rectangle, get_foot_height, find_max_inner_width
import layoutparser as lp


if __name__ == "__main__":


    # Ensure the script is run with a Python 3 interpreter
    if sys.version_info < (3, 0):
        raise RuntimeError("This script requires Python 3.x")

    # Get the directory of the current script
    script_dir = Path(__file__).parent
    model_directory = str((script_dir / "../models").resolve())

    image_directory = script_dir / "../images"
    if not image_directory.exists():
        raise FileNotFoundError(f"Image directory does not exist: {image_directory}")
    # Ensure the images directory is a valid directory
    if not image_directory.is_dir():
        raise NotADirectoryError(f"Expected a directory but found: {image_directory}")
    # Ensure the images directory is not empty
    if not any(image_directory.iterdir()):
        raise RuntimeError(f"No images found in directory: {image_directory}")
    

    detector = PageDetector()
    model = lp.models.Detectron2LayoutModel(
        config_path= model_directory + '/config.yml',
        model_path=model_directory + '/model.pth',
        extra_config=['MODEL.ROI_HEADS.SCORE_THRESH_TEST', 0.8],
        label_map={0: 'Page'}
    )
    approximator = QuadrilateralApproximator()
    ### for each image in the directory, process it
    preprocessor = ImagePreprocessor(model)
    for image_path in image_directory.glob("*.webp"):
        if not image_path.is_file():
            print(f"Skipping non-file: {image_path}")
            continue

        try:
            # Initialize the image preprocessor with a layout model
            
            # Load and crop the image
            roi = preprocessor.load_and_crop(image_path)

            # Detect the page contour
            
            page_contour = detector.detect_contour(roi)

            # Approximate the quadrilateral
            
            quad = approximator.get_quadrilateral(page_contour)
            quad, scale_y, scale_x = approximator.refine_quad_approximation(quad)

            # Visualize contours and quadrilateral
            cropped_roi, adjusted_page_contour, adjusted_rect = crop_image_contour_rectangle(roi.copy(), page_contour.copy(), quad.copy())


            foot_height = 297 - (get_foot_height(adjusted_rect, adjusted_page_contour, min_depth=50) * scale_y)

            best = find_max_inner_width(quad, page_contour.squeeze(1),
                            buffer=20, tol=2.0, n_steps=100)
            feet_width = best['contour_width'] * scale_x

            print(f"Foot height: {foot_height}, Feet width: {feet_width}")



        except Exception as e:
            print(f"Error processing {image_path}: {e}")

    # Add the parent directory to the system path
    sys.path.append(str(script_dir.parent))

    # Import and run the main function from the module
    print("Script executed successfully.")
    sys.exit(0)