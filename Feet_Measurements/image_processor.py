import layoutparser as lp
import cv2
import numpy as np
from pathlib import Path


class ImagePreprocessor:
    """Improved with adaptive thresholding and better padding calculation"""
    def __init__(self, model: lp.models.Detectron2LayoutModel) -> None:
        self.model = model

    def load_and_crop(self, image_path: Path) -> np.ndarray:
        img_bgr = cv2.imread(str(image_path))
        if img_bgr is None:
            raise FileNotFoundError(f"Image not found: {image_path}")

        # Convert to grayscale for better layout detection
        img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)

        layout = self.model.detect(img_rgb)
        pages = pages = [block for block in layout._blocks]

        if not pages:
            raise RuntimeError("No page detected in layout")

        page = max(pages, key=lambda x: x.score)
        x1, y1, x2, y2 = map(int, page.coordinates)

        # Dynamic padding based on image resolution
        pad = int(0.02 * max(img_bgr.shape[:2]))
        pad_x = int(pad * (img_bgr.shape[1] / img_bgr.shape[0]))
        pad_y = int(pad * (img_bgr.shape[0] / img_bgr.shape[1]))

        roi = img_bgr[max(0, y1-pad_y):min(img_bgr.shape[0], y2+pad_y),
                      max(0, x1-pad_x):min(img_bgr.shape[1], x2+pad_x)]
        return roi