import cv2
import numpy as np
from typing import Tuple

def order_points(pts: np.ndarray) -> np.ndarray:
    """
    Order 4 points in the order: top-left, top-right, bottom-right, bottom-left.
    """
    rect = np.zeros((4, 2), dtype="float32")
    s = pts.sum(axis=1)
    diff = np.diff(pts, axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    return rect


def visualise_contours(image: np.ndarray, contours: list) -> np.ndarray:
    """
    Visualise contours on an image.
    """
    for contour in contours:
        cv2.drawContours(image, [contour], -1, (0, 255, 0), 2)
    return image

def visualise_quad(image: np.ndarray, quad: np.ndarray) -> np.ndarray:
    """
    Visualise a quadrilateral on an image.
    """
    cv2.polylines(image, [quad.astype(np.int32)], isClosed=True, color=(0, 0, 255), thickness=2)
    return image


def crop_image_contour_rectangle(image: np.ndarray, contour: np.ndarray, rectangle: np.ndarray, percent = 0.4) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Crops the bottom 40% of the image, adjusts contour points and the bounding rectangle.

    Args:
        image: The input image as a NumPy array.
        contour: The contour points as a NumPy array (N, 1, 2) or (N, 2).
        rectangle: The bounding rectangle points as a NumPy array (4, 1, 2) or (4, 2).

    Returns:
        A tuple containing the cropped image, adjusted contour, and adjusted rectangle.
    """
    height, width = image.shape[:2]
    crop_height = int(height * (1 - percent))  # Keep the top 60%

    # Crop the image
    cropped_image = image[:crop_height, :]

    # Adjust contour points
    # Only keep points that are above the crop line
    adjusted_contour_list = []
    contour_reshaped = contour.reshape(-1, 2)
    for point in contour_reshaped:
        if point[1] < crop_height:
            adjusted_contour_list.append(point)
    adjusted_contour = np.array(adjusted_contour_list, dtype=np.int32).reshape(-1, 1, 2) if adjusted_contour_list else np.array([], dtype=np.int32).reshape(-1, 1, 2)


    # Adjust rectangle points
    adjusted_rectangle_list = []
    rectangle_reshaped = rectangle.reshape(-1, 2)
    for point in rectangle_reshaped:
        # If a rectangle point is below the crop line, move it to the crop line
        adjusted_y = min(point[1], crop_height -1 ) # -1 to stay within bounds
        adjusted_rectangle_list.append([point[0], adjusted_y])

    adjusted_rectangle = np.array(adjusted_rectangle_list, dtype=np.int32).reshape(-1, 1, 2)


    return cropped_image, adjusted_contour, adjusted_rectangle



def sort_quad_corners(quad):
    """
    Orders quad corners in consistent order (tl, tr, br, bl).
    quad: array of shape (4,2)
    """
    pts = quad.reshape(-1, 2)
    s = pts.sum(axis=1)
    diff = np.diff(pts, axis=1).ravel()
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    return np.array([tl, tr, br, bl], dtype=np.float32)

def edge_unit_vectors(a, b, inward_point):
    """
    Returns unit edge vector u = (b-a)/|b-a| and inward normal n.
    inward_point: a point known to be “inside” the quad (e.g. its centroid).
    """
    # edge unit vector
    v = b - a
    u = v / np.linalg.norm(v)
    # normal (rotate u by +90°)
    n = np.array([-u[1], u[0]])
    # ensure n points toward inward_point
    if np.dot((inward_point - a), n) < 0:
        n = -n
    return u, n



def min_gap_to_edge(contour_pts, a, b, n, u, min_depth=50):
    """
    Returns the minimal projection onto n of those contour_pts which
    project onto segment [a,b] and have depth >= min_depth.
    """
    # vectors from a to all contour points
    v = contour_pts - a[None, :]
    s = v.dot(u)                # along-edge distances
    d = v.dot(n)                # perpendicular distances
    # filter to those that drop onto the segment and are beyond noise
    mask = (s >= 0) & (s <= np.linalg.norm(b - a)) & (d >= min_depth)
    if not np.any(mask):
        return None             # no valid points
    return d[mask].min()


def compute_quad_contour_top_diff(quad, contour, min_depth=50):
    """
    quad: np.array of shape (4,1,2) or (4,2)
    contour: np.array of shape (N,1,2) or (N,2)
    Returns dict of gaps for edges ['top','right','bottom','left'].
    """
    # normalize shapes
    quad_pts = quad.reshape(-1, 2).astype(np.float32)
    cnt_pts  = contour.reshape(-1, 2).astype(np.float32)
    # order quad and compute centroid
    tl, tr, br, bl = sort_quad_corners(quad_pts)
    centroid = np.mean([tl, tr, br, bl], axis=0)
    # define edges in clockwise order
    # edges = {
    #     'top':    (tl, tr),
    #     'right':  (tr, br),
    #     'bottom': (br, bl),
    #     'left':   (bl, tl),
    # }
    u, n = edge_unit_vectors(tl, tr, centroid)
    gap = min_gap_to_edge(cnt_pts, tl, tr, n, u, min_depth)
    return gap



def get_foot_height(quad, contour, min_depth=50):
    """
    Computes the foot width and height of a quadrilateral based on contour gaps.
    quad: np.array of shape (4,1,2) or (4,2)
    contour: np.array of shape (N,1,2) or (N,2)
    Returns a tuple (foot_width, foot_height).
    """
    foot_height = compute_quad_contour_top_diff(quad, contour, min_depth)
    return foot_height


def get_boundary_xs_at_y(quad, y):
    """
    Given quad: array-like shape (4,2), and a y-value,
    returns the two x-coordinates where the horizontal line y=... intersects the quad edges.
    """
    intersections = []
    for i in range(4):
        x1, y1 = quad[i]
        x2, y2 = quad[(i+1) % 4]
        # check if the segment [y1,y2] straddles the horizontal line
        if (y1 - y) * (y2 - y) <= 0 and (y2 != y1):
            t = (y - y1) / (y2 - y1)
            x = x1 + t * (x2 - x1)
            intersections.append(x)
    if len(intersections) != 2:
        raise ValueError(f"Scanline at y={y} intersects quad in {len(intersections)} points (expected 2).")
    return sorted(intersections)


def measure_width_at_fraction(quad, contour, y_frac=0.6, buffer=50, tol=1.0):
    """
    quad: np.array shape (4,2)
    contour: np.array shape (N,2)
    y_frac: fraction down from top edge (0=top, 1=bottom)
    buffer: pixels to exclude within that distance of quad edges
    tol: vertical tolerance (in px) for selecting contour points

    Returns a dict with:
      y, x_left_edge, x_right_edge,
      x_left_contour, x_right_contour,
      left_dist, right_dist, contour_width
    """
    quad = np.asarray(quad, float)
    contour = np.asarray(contour, float)

    # find vertical range of the quad
    y_top = np.min(quad[:,1])
    y_bot = np.max(quad[:,1])
    y = y_top + y_frac * (y_bot - y_top)

    # 1) where does the scanline hit the quad?
    x_left_edge, x_right_edge = get_boundary_xs_at_y(quad, y)

    # 2) pick contour points near that scanline
    ys = contour[:,1]
    xs = contour[:,0]
    mask_y = np.abs(ys - y) <= tol
    xs_near = xs[mask_y]
    if xs_near.size < 2:
        raise RuntimeError("Not enough contour points at this y (try increasing tol).")

    # 3) drop any that are too close to the quad edges
    mask_buf = (xs_near >= x_left_edge + buffer) & (xs_near <= x_right_edge - buffer)
    xs_buf = xs_near[mask_buf]
    if xs_buf.size < 2:
        raise RuntimeError("After buffering, not enough contour points (reduce buffer?).")

    # 4) extremes and distances
    x_left_contour  = xs_buf.min()
    x_right_contour = xs_buf.max()
    left_dist  = x_left_contour  - x_left_edge
    right_dist = x_right_edge    - x_right_contour
    contour_width = x_right_contour - x_left_contour

    return {
        'y': y,
        'x_left_edge': x_left_edge,
        'x_right_edge': x_right_edge,
        'x_left_contour': x_left_contour,
        'x_right_contour': x_right_contour,
        'left_dist': left_dist,
        'right_dist': right_dist,
        'contour_width': contour_width
    }


def find_max_inner_width(quad, contour, buffer=50, tol=1.0, n_steps= 50):
    """
    Optionally scan many y-fractions (from 0.1 to 0.9) to find
    the horizontal slice where the inner contour is widest (i.e. margins minimal).
    """
    best = None
    best_width = -np.inf
    for frac in np.linspace(0.1, 0.7, n_steps):
        try:
            m = measure_width_at_fraction(quad, contour, y_frac=frac,
                                          buffer=buffer, tol=tol)
            if m['contour_width'] > best_width:
                best_width = m['contour_width']
                best = m
        except RuntimeError:
            continue
    if best is None:
        raise RuntimeError("Could not find any valid slice.")
    return best