#!/usr/bin/env python3
"""
Test script for EigenFit Foot Measurement API
"""

import requests
import json
from pathlib import Path
import sys

def test_health_endpoint(base_url="http://localhost:8000"):
    """Test the health check endpoint."""
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Is the server running?")
        return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_root_endpoint(base_url="http://localhost:8000"):
    """Test the root endpoint."""
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ Root endpoint accessible")
            data = response.json()
            print(f"API: {data.get('message', 'Unknown')}")
            print(f"Version: {data.get('version', 'Unknown')}")
            return True
        else:
            print(f"❌ Root endpoint failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
        return False

def test_measure_endpoint_with_sample_images(base_url="http://localhost:8000"):
    """Test the measure endpoint with sample images."""
    # Look for sample images in the ExampleImages directory
    sample_dirs = [
        "ExampleImages/LeftTop",
        "ExampleImages/RightTop", 
        "ExampleImages/LeftInner",
        "ExampleImages/RightInner"
    ]
    
    image_files = []
    for dir_path in sample_dirs:
        dir_obj = Path(dir_path)
        if dir_obj.exists():
            # Get the first image file in each directory
            for ext in ["*.webp", "*.jpg", "*.jpeg", "*.png"]:
                files = list(dir_obj.glob(ext))
                if files:
                    image_files.append(files[0])
                    break
    
    if len(image_files) < 4:
        print(f"❌ Not enough sample images found. Need 4, found {len(image_files)}")
        print("Available sample images:")
        for img in image_files:
            print(f"  - {img}")
        return False
    
    print(f"📸 Testing with sample images:")
    for i, img in enumerate(image_files[:4]):
        print(f"  {i+1}. {img}")
    
    try:
        # Prepare files for upload
        files = []
        for i, img_path in enumerate(image_files[:4]):
            with open(img_path, 'rb') as f:
                files.append(('images', (f'image_{i}.webp', f.read(), 'image/webp')))
        
        # Make the request
        response = requests.post(f"{base_url}/measure", files=files)
        
        if response.status_code == 200:
            print("✅ Measurement endpoint test passed")
            result = response.json()
            print("📏 Measurement Results:")
            print(json.dumps(result, indent=2))
            return True
        else:
            print(f"❌ Measurement failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Measurement endpoint error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing EigenFit Foot Measurement API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"Testing API at: {base_url}")
    print()
    
    # Run tests
    tests = [
        ("Health Check", test_health_endpoint),
        ("Root Endpoint", test_root_endpoint),
        ("Measure Endpoint", test_measure_endpoint_with_sample_images)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🔍 Running {test_name}...")
        try:
            result = test_func(base_url)
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Test Summary:")
    print("=" * 30)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the API server and try again.")
        return 1

if __name__ == "__main__":
    exit(main())
