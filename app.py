from fastapi import FastAPI, UploadFile, File, HTTPException
from eigenfit_image_analysis_template import EigenFitSession
import tempfile, shutil, json, os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="EigenFit Foot-Measurement API",
    description="API for measuring foot dimensions from smartphone images",
    version="1.0.0"
)


@app.get("/health")
async def health_check():
    """Health check endpoint for container monitoring."""
    return {"status": "healthy", "service": "eigenfit-foot-measurement"}


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "EigenFit Foot Measurement API",
        "version": "1.0.0",
        "endpoints": {
            "measure": "/measure - POST endpoint for foot measurement",
            "health": "/health - Health check endpoint"
        }
    }


@app.post("/measure")
async def measure(images: list[UploadFile] = File(...)):
    """
    Measure foot dimensions from 4 smartphone images.

    Expected images in order:
    1. Left foot from top view
    2. Right foot from top view
    3. Left foot inner side view (for arch calculation)
    4. Right foot inner side view (for arch calculation)

    Returns JSON with measurements for both feet.
    """
    if len(images) != 4:
        raise HTTPException(status_code=400, detail="Exactly 4 images required")

    # Validate file types
    allowed_types = {"image/jpeg", "image/jpg", "image/png", "image/webp"}
    for img in images:
        if img.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {img.content_type}. Allowed: {allowed_types}"
            )

    # Persist the uploads to temporary files — EigenFitSession works with paths
    tmp_dir = tempfile.mkdtemp()
    img_paths = []
    try:
        logger.info(f"Processing {len(images)} images for foot measurement")

        for i, f in enumerate(images):
            # Use a standardized filename to avoid issues
            filename = f"image_{i}.{f.filename.split('.')[-1]}" if f.filename else f"image_{i}.jpg"
            dest = os.path.join(tmp_dir, filename)

            with open(dest, "wb") as out:
                content = await f.read()
                out.write(content)
                logger.info(f"Saved image {i+1}: {filename} ({len(content)} bytes)")

            img_paths.append(dest)

        session = EigenFitSession(img_paths, debug=False)
        result = session.run()

        logger.info("Foot measurement completed successfully")
        return result

    except Exception as e:
        logger.error(f"Error processing foot measurement: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")
    finally:
        shutil.rmtree(tmp_dir, ignore_errors=True)
