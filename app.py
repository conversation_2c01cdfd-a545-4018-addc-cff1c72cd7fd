from fastapi import Fast<PERSON><PERSON>, UploadFile, File, HTTPException
from eigenfit_image_analysis_template import EigenFitSession
import tempfile, shutil, json, os

app = FastAPI(title="EigenFit Foot-Measurement API")


@app.post("/measure")
async def measure(images: list[UploadFile] = File(...)):
    if len(images) != 4:
        raise HTTPException(status_code=400, detail="Exactly 4 images required")

    # Persist the uploads to temporary files — EigenFitSession works with paths
    tmp_dir = tempfile.mkdtemp()
    img_paths = []
    try:
        for f in images:
            dest = os.path.join(tmp_dir, f.filename)
            with open(dest, "wb") as out:
                out.write(await f.read())
            img_paths.append(dest)

        session = EigenFitSession(img_paths, debug=False)
        result = session.run()
        return result
    finally:
        shutil.rmtree(tmp_dir, ignore_errors=True)
