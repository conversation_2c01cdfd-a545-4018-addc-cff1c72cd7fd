#!/usr/bin/env python3
"""
Development server startup script for EigenFit Foot Measurement API
"""

import uvicorn
import sys
import os
from pathlib import Path

def main():
    """Start the development server."""
    # Add current directory to Python path
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    # Check if required files exist
    required_files = [
        "eigenfit_image_analysis_template.py",
        "app.py",
        "models/config.yml",
        "models/model.pth"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\nPlease ensure all required files are present before starting the server.")
        return 1
    
    print("🚀 Starting EigenFit Foot Measurement API Server")
    print("=" * 50)
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("=" * 50)
    
    # Start the server
    try:
        uvicorn.run(
            "app:app",
            host="0.0.0.0",
            port=8000,
            reload=True,  # Enable auto-reload for development
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return 0
    except Exception as e:
        print(f"❌ Server error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
